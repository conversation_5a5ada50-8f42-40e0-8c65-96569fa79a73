/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/name-colors/route";
exports.ids = ["app/api/name-colors/route"];
exports.modules = {

/***/ "(rsc)/./app/api/name-colors/route.ts":
/*!**************************************!*\
  !*** ./app/api/name-colors/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_color_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/color-service */ \"(rsc)/./lib/color-service.ts\");\n\n\n// Maximum number of colors that can be named at once\nconst MAX_COLORS = 20;\nasync function POST(req) {\n    try {\n        const { colors } = await req.json();\n        // Validate input\n        if (!colors || !Array.isArray(colors) || colors.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"No colors provided\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate array length\n        if (colors.length > MAX_COLORS) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Too many colors\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate each color\n        if (!colors.every((color)=>typeof color === 'string' && /^#[0-9A-Fa-f]{6}$/.test(color))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid color format\"\n            }, {\n                status: 400\n            });\n        }\n        // Get API key from environment variables\n        const apiKey = process.env.GEMINI_API_KEY;\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Service temporarily unavailable\"\n            }, {\n                status: 503\n            });\n        }\n        // Generate a prompt for naming colors\n        const prompt = (0,_lib_color_service__WEBPACK_IMPORTED_MODULE_1__.generateColorNamingPrompt)(colors);\n        // Make request to Gemini API\n        const response = await fetch(`https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                contents: [\n                    {\n                        parts: [\n                            {\n                                text: prompt\n                            }\n                        ]\n                    }\n                ],\n                generationConfig: {\n                    temperature: 0.2,\n                    maxOutputTokens: 1024\n                }\n            })\n        });\n        if (!response.ok) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to process colors\"\n            }, {\n                status: 500\n            });\n        }\n        const data = await response.json();\n        // Parse the response to extract the colors JSON\n        const namedColorsText = data.candidates[0]?.content?.parts[0]?.text || \"\";\n        try {\n            // Parse the colors from the response\n            const namedColors = await (0,_lib_color_service__WEBPACK_IMPORTED_MODULE_1__.parseColorsFromResponse)(namedColorsText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                colors: namedColors\n            });\n        } catch (e) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to process color names\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"An unexpected error occurred\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/name-colors/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/color-service.ts":
/*!******************************!*\
  !*** ./lib/color-service.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatColorForDisplay: () => (/* binding */ formatColorForDisplay),\n/* harmony export */   generateColorExtractionPrompt: () => (/* binding */ generateColorExtractionPrompt),\n/* harmony export */   generateColorNamingPrompt: () => (/* binding */ generateColorNamingPrompt),\n/* harmony export */   parseColorsFromResponse: () => (/* binding */ parseColorsFromResponse)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(rsc)/./lib/utils.ts\");\n\n/**\r\n * Parses colors from Gemini API response text\r\n */ async function parseColorsFromResponse(text) {\n    // Extract JSON array from the response if needed\n    const jsonMatch = text.match(/\\[[\\s\\S]*\\]/);\n    const colorsJson = jsonMatch ? jsonMatch[0] : text;\n    try {\n        const colors = JSON.parse(colorsJson);\n        // Validate each color has name and hex properties\n        const validColors = colors.filter((color)=>color && typeof color.name === \"string\" && typeof color.hex === \"string\");\n        // Add tailwind property for each color\n        return validColors.map((color)=>({\n                ...color,\n                tailwind: color.name.toLowerCase().replace(/\\s+/g, \"-\")\n            }));\n    } catch (e) {\n        console.error(\"Failed to parse colors:\", e);\n        throw new Error(`Failed to parse color data: ${text}`);\n    }\n}\n/**\r\n * Generates a prompt for Gemini API to extract colors\r\n */ function generateColorExtractionPrompt(existingColors = [], maxColors = 5) {\n    let prompt = `Analyze this image and extract up to ${maxColors} most prominent colors. For each color, provide the name and hex code. Format your response as a JSON array of objects with 'name' and 'hex' properties. Example: [{\\\"name\\\":\\\"Blue\\\",\\\"hex\\\":\\\"#0000FF\\\"},{\\\"name\\\":\\\"Red\\\",\\\"hex\\\":\\\"#FF0000\\\"}]. Only return the JSON array, nothing else.`;\n    // If we have existing colors, ask for different colors\n    if (existingColors.length > 0) {\n        prompt = `Analyze this image and extract up to ${maxColors} unique and visually distinct colors that are NOT similar to the following colors already in the palette: ${existingColors.join(', ')}. \n    \nLook for colors that add diversity to the palette and represent different areas of the image. For each color, provide the name and hex code. Format your response as a JSON array of objects with 'name' and 'hex' properties. Example: [{\\\"name\\\":\\\"Blue\\\",\\\"hex\\\":\\\"#0000FF\\\"},{\\\"name\\\":\\\"Red\\\",\\\"hex\\\":\\\"#FF0000\\\"}]. Only return the JSON array, nothing else.`;\n    }\n    return prompt;\n}\n/**\r\n * Formats color for display\r\n */ function formatColorForDisplay(color) {\n    // Ensure hex code is uppercase and has # prefix\n    let hex = color.hex.toUpperCase();\n    if (!hex.startsWith(\"#\")) {\n        hex = `#${hex}`;\n    }\n    // Generate name if not provided\n    const name = color.name || (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getColorName)(hex);\n    // Generate tailwind class name if not provided\n    const tailwind = color.tailwind || name.toLowerCase().replace(/\\s+/g, \"-\");\n    return {\n        name,\n        hex,\n        tailwind\n    };\n}\n/**\r\n * Generates a prompt for naming colors using AI\r\n */ function generateColorNamingPrompt(colors) {\n    return `For each of the following hex colors, provide a descriptive, professional color name that would be suitable for a design palette. Return the results as a JSON array of objects with 'hex' and 'name' properties. ONLY return the JSON array.\n\nColors to name: ${colors.join(', ')}\n\nExample response format:\n[\n  {\"hex\": \"#FF0000\", \"name\": \"Crimson Red\"},\n  {\"hex\": \"#0000FF\", \"name\": \"Royal Blue\"}\n]`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/color-service.ts\n");

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLOR_NAMES: () => (/* binding */ COLOR_NAMES),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   getColorName: () => (/* binding */ getColorName),\n/* harmony export */   hexToRgb: () => (/* binding */ hexToRgb),\n/* harmony export */   rgbToHex: () => (/* binding */ rgbToHex),\n/* harmony export */   rgbToHsl: () => (/* binding */ rgbToHsl)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n// Utility function for combining class names\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Convert RGB to HEX\nfunction rgbToHex(r, g, b) {\n    return \"#\" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();\n}\n// Convert HEX to RGB\nfunction hexToRgb(hex) {\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n    return result ? {\n        r: parseInt(result[1], 16),\n        g: parseInt(result[2], 16),\n        b: parseInt(result[3], 16)\n    } : null;\n}\n// Convert RGB to HSL\nfunction rgbToHsl(r, g, b) {\n    r /= 255;\n    g /= 255;\n    b /= 255;\n    const max = Math.max(r, g, b);\n    const min = Math.min(r, g, b);\n    let h = 0;\n    let s = 0;\n    const l = (max + min) / 2;\n    if (max !== min) {\n        const d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch(max){\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n        }\n        h /= 6;\n    }\n    return {\n        h: h * 360,\n        s: s * 100,\n        l: l * 100\n    };\n}\n// Color name mapping based on HSL values\nconst COLOR_NAMES = {\n    // Reds\n    red: \"#FF0000\",\n    crimson: \"#DC143C\",\n    maroon: \"#800000\",\n    tomato: \"#FF6347\",\n    coral: \"#FF7F50\",\n    salmon: \"#FA8072\",\n    // Oranges\n    orange: \"#FFA500\",\n    gold: \"#FFD700\",\n    amber: \"#FFBF00\",\n    // Yellows\n    yellow: \"#FFFF00\",\n    khaki: \"#F0E68C\",\n    lemon: \"#FFF700\",\n    // Greens\n    green: \"#008000\",\n    lime: \"#00FF00\",\n    olive: \"#808000\",\n    teal: \"#008080\",\n    emerald: \"#50C878\",\n    mint: \"#3EB489\",\n    sage: \"#BCB88A\",\n    // Blues\n    blue: \"#0000FF\",\n    navy: \"#000080\",\n    azure: \"#007FFF\",\n    cyan: \"#00FFFF\",\n    turquoise: \"#40E0D0\",\n    skyblue: \"#87CEEB\",\n    cobalt: \"#0047AB\",\n    // Purples\n    purple: \"#800080\",\n    violet: \"#8F00FF\",\n    magenta: \"#FF00FF\",\n    lavender: \"#E6E6FA\",\n    indigo: \"#4B0082\",\n    // Browns\n    brown: \"#A52A2A\",\n    chocolate: \"#D2691E\",\n    tan: \"#D2B48C\",\n    beige: \"#F5F5DC\",\n    // Neutrals\n    black: \"#000000\",\n    gray: \"#808080\",\n    silver: \"#C0C0C0\",\n    white: \"#FFFFFF\",\n    ivory: \"#FFFFF0\",\n    cream: \"#FFFDD0\"\n};\n// Get color name based on closest match\nfunction getColorName(hex) {\n    const rgb = hexToRgb(hex);\n    if (!rgb) return \"Unknown\";\n    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);\n    // Determine brightness and saturation categories\n    const brightness = hsl.l;\n    const saturation = hsl.s;\n    let prefix = \"\";\n    let baseName = \"\";\n    // Determine prefix based on lightness and saturation\n    if (brightness < 20) prefix = \"Dark \";\n    else if (brightness > 80) prefix = \"Light \";\n    else if (saturation < 10) prefix = \"Muted \";\n    else if (saturation > 80) prefix = \"Vibrant \";\n    // Find the closest color name by comparing hex values\n    let minDistance = Number.MAX_VALUE;\n    for (const [name, colorHex] of Object.entries(COLOR_NAMES)){\n        const namedRgb = hexToRgb(colorHex);\n        if (!namedRgb) continue;\n        // Calculate color distance using simple Euclidean distance in RGB space\n        const distance = Math.sqrt(Math.pow(namedRgb.r - rgb.r, 2) + Math.pow(namedRgb.g - rgb.g, 2) + Math.pow(namedRgb.b - rgb.b, 2));\n        if (distance < minDistance) {\n            minDistance = distance;\n            baseName = name.charAt(0).toUpperCase() + name.slice(1);\n        }\n    }\n    // If the color is very close to a named color, don't use a prefix\n    if (minDistance < 30) {\n        return baseName;\n    }\n    return prefix + baseName;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fname-colors%2Froute&page=%2Fapi%2Fname-colors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fname-colors%2Froute.ts&appDir=E%3A%5CWeWiseLabs%5Ccoloriqo1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWeWiseLabs%5Ccoloriqo1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fname-colors%2Froute&page=%2Fapi%2Fname-colors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fname-colors%2Froute.ts&appDir=E%3A%5CWeWiseLabs%5Ccoloriqo1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWeWiseLabs%5Ccoloriqo1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_WeWiseLabs_coloriqo1_0_app_api_name_colors_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/name-colors/route.ts */ \"(rsc)/./app/api/name-colors/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/name-colors/route\",\n        pathname: \"/api/name-colors\",\n        filename: \"route\",\n        bundlePath: \"app/api/name-colors/route\"\n    },\n    resolvedPagePath: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\api\\\\name-colors\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_WeWiseLabs_coloriqo1_0_app_api_name_colors_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fname-colors%2Froute&page=%2Fapi%2Fname-colors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fname-colors%2Froute.ts&appDir=E%3A%5CWeWiseLabs%5Ccoloriqo1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWeWiseLabs%5Ccoloriqo1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fname-colors%2Froute&page=%2Fapi%2Fname-colors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fname-colors%2Froute.ts&appDir=E%3A%5CWeWiseLabs%5Ccoloriqo1.0%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWeWiseLabs%5Ccoloriqo1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();