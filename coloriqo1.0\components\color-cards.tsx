"use client"

import React, { useState, useRef, useEffect, useMemo, useCallback } from "react"
import { Card, CardContent} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Upload, Pipette, Copy, Check, Download, Cpu, Hand, Trash2, LayoutPanelTop, Code, CreditCard, Clock, RefreshCw, LogIn, LogOut } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { toast } from "@/hooks/use-toast"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useColorExtractor } from "@/hooks/use-color-extractor"
import { Loader } from "@/components/ui/loader"
import { Color, ExtractionMethod } from "@/types"
import { Separator } from "@/components/ui/separator"
import { CodePreview } from "./ui/code-preview"
import { Magnifier } from "./ui/magnifier"
import { ThemeSwitcher } from "./ui/theme-switcher"
import { 
  getColorName,
  rgbToHex,
} from "@/lib/utils"
import { 
  getCreditState, 
  useCredit, 
  resetCredits,
  formatTimeRemaining,
  getCreditRegenerationPeriod,
  getFullRegenerationTime
} from "@/lib/credit-service"
import Link from "next/link"

export default function ColorCards() {
  // Constants
  const COLOR_LIMIT_STANDARD = 10
  const ADMIN_CODE = process.env.NEXT_PUBLIC_ADMIN_CODE
  if (!ADMIN_CODE) {
    throw new Error('NEXT_PUBLIC_ADMIN_CODE environment variable is required')
  }
  const COLORS_PER_CREDIT = 5 // Number of colors per credit
  
  // Constants for session storage
  const SESSION_COLORS_KEY = "color-tool-colors";
  const SESSION_IMAGE_KEY = "color-tool-image";
  
  // State to track if session storage should be cleared
  const [sessionCleared, setSessionCleared] = useState(false);
  
  // Memoize session storage operations with sessionCleared dependency
  const storedColors = useMemo(() => {
    if (typeof window !== 'undefined' && !sessionCleared) {
      try {
        const storedColors = sessionStorage.getItem(SESSION_COLORS_KEY);
        if (storedColors) {
          return JSON.parse(storedColors);
        }
      } catch (error) {
        // Silent fail - start with empty colors
      }
    }
    return [] as Color[];
  }, [sessionCleared]);
  
  // Memoize image retrieval with sessionCleared dependency
  const storedImage = useMemo(() => {
    if (typeof window !== 'undefined' && !sessionCleared) {
      try {
        return sessionStorage.getItem(SESSION_IMAGE_KEY);
      } catch (error) {
        // Silent fail - start with no image
      }
    }
    return null;
  }, [sessionCleared]);
  
  // States
  const [extractedColors, setExtractedColors] = useState<Color[]>(storedColors)
  const [copiedColor, setCopiedColor] = useState<string | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [extractionMethod, setExtractionMethod] = useState<ExtractionMethod>("ai")
  const [activeTab, setActiveTab] = useState<string>("upload")
  const [showCodePreview, setShowCodePreview] = useState(false)
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null)
  const [showMagnifier, setShowMagnifier] = useState(false)
  const [currentPixelColor, setCurrentPixelColor] = useState<string>("")
  const [isAdminMode, setIsAdminMode] = useState(false)
  const [adminCodeInput, setAdminCodeInput] = useState("")
  const [showAdminInput, setShowAdminInput] = useState(false)
  const [creditState, setCreditState] = useState(() => getCreditState())
  const [timeRemaining, setTimeRemaining] = useState<string>("")
  const [manualPicksCount, setManualPicksCount] = useState(0) // Count manual color picks
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false) // State for mobile menu toggle
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Create ref outside of useEffect to track credit state
  const creditStateRef = useRef(creditState);
  
  // Update ref whenever creditState changes
  useEffect(() => {
    creditStateRef.current = creditState;
  }, [creditState]);
  
  // Update credit info and handle timer countdown
  useEffect(() => {
    // Initial state fetch
    const initialState = getCreditState(true);
    setCreditState(initialState);
    
    // Display and update timer
    const timerInterval = setInterval(() => {
      // Get current credit state
      const currentState = getCreditState(false);
      
      // If there's a regeneration time, show countdown
      if (currentState.nextRegenerationTime > 0) {
        // Calculate and display time remaining
        const timeLeft = formatTimeRemaining(currentState.nextRegenerationTime);
        setTimeRemaining(timeLeft);
        
        // Check if time is up (credits should regenerate)
        if (timeLeft === "now") {
          // Force refresh to get updated credits
          const refreshedState = getCreditState(true);
          
          // Only update state if credits actually changed
          if (refreshedState.credits !== creditStateRef.current.credits) {
            setCreditState(refreshedState);
            
            // Show notification only if credits increased
            if (refreshedState.credits > creditStateRef.current.credits) {
              toast({
                title: "Credits refreshed!",
                description: "You have new credits available.",
                duration: 3000,
              });
            }
          }
        }
      } else {
        // No regeneration time, clear the timer display
        setTimeRemaining("");
      }
      
      // Only update credit state if it has actually changed
      if (currentState.credits !== creditStateRef.current.credits || 
          currentState.nextRegenerationTime !== creditStateRef.current.nextRegenerationTime) {
        setCreditState(currentState);
      }
    }, 1000); // Update every second
    
    return () => {
      clearInterval(timerInterval);
    };
  }, []); // No dependencies to avoid re-creating the interval
  
  // Update credit state - memoize this function to improve performance
  const updateCreditState = useCallback((forceRefresh: boolean = false) => {
    const newState = getCreditState(forceRefresh);
    setCreditState(newState);
    return newState;
  }, []);
  
  // Show current credit status
  const showCreditStatus = () => {
    const state = updateCreditState();
    
    toast({
      title: "Credit Status",
      description: (
        <div className="flex flex-col gap-1">
          <p>Available credits: {state.credits}</p>
          {state.nextRegenerationTime > 0 && (
            <p>Credits will reset at: {formatTimeRemaining(state.nextRegenerationTime)}</p>
          )}
          {state.nextRegenerationTime > 0 && (
            <p className="text-xs text-muted-foreground mt-1">{getFullRegenerationTime(state.nextRegenerationTime)}</p>
          )}
        </div>
      ),
      duration: 7000,
    });
  };
  
  // Use a credit for extraction
  const consumeCredit = () => {
    if (isAdminMode) {
      return true; // Admin has unlimited credits
    }
    
    // Get fresh state directly from service
    const currentState = getCreditState(true);
    
    if (currentState.credits > 0) {
      try {
        // Use credit and get new state
        const newState = useCredit();
        
        // Update component state
        setCreditState(newState);
        
        // Show toast to confirm credit usage
        toast({
          title: "Credit used",
          description: `1 credit consumed. ${newState.credits} credits remaining.`,
          duration: 3000,
        });
        
        return true;
      } catch (error) {
        toast({
          title: "Error",
          description: "Could not process credit. Please try again.",
          variant: "destructive",
          duration: 3000,
        });
        return false;
      }
    }
    return false;
  };
  
  // Update credits based on admin status
  useEffect(() => {
    if (isAdminMode) {
      // Reset credits if admin mode is activated
      resetCredits();
      updateCreditState();
    }
  }, [isAdminMode]);
  
  // Listen for custom event to open admin input
  useEffect(() => {
    const handleOpenAdminInput = () => {
      setShowAdminInput(true)
    }
    window.addEventListener('openAdminInput', handleOpenAdminInput)
    return () => {
      window.removeEventListener('openAdminInput', handleOpenAdminInput)
    }
  }, [])

  // Clean up any resources on unmount
  useEffect(() => {
    return () => {
      // Clean up any ObjectURLs or other resources
      if (selectedFile) {
        setSelectedFile(null)
      }
    }
  }, [])

  // Save colors to session storage when they change - debounced to reduce writes
  useEffect(() => {
    // Use a timeout to debounce multiple quick updates
    const saveTimeout = setTimeout(() => {
      if (typeof window !== 'undefined' && extractedColors.length > 0) {
        try {
          sessionStorage.setItem(SESSION_COLORS_KEY, JSON.stringify(extractedColors));
        } catch (error) {
          // Silent fail - colors will be lost on refresh
        }
      }
    }, 300); // Debounce for 300ms
    
    // Clear timeout on cleanup
    return () => clearTimeout(saveTimeout);
  }, [extractedColors]);
  
  // Use our custom hook for color extraction
  const {
    isExtracting,
    selectedImage,
    setSelectedImage,
    isPickingColor,
    extractionError,
    imageSize,
    toggleColorPicker,
    handleFileSelect,
    extractColors,
  } = useColorExtractor({
    onExtractedColors: (colors) => {
      setExtractedColors(prev => {
        const newColors = [...prev, ...colors];
        // Save to session storage if not cleared
        if (!sessionCleared && typeof window !== 'undefined') {
          sessionStorage.setItem(SESSION_COLORS_KEY, JSON.stringify(newColors));
        }
        return newColors;
      });
    },
    canvasRef,
    existingColors: extractedColors,
    isAdminMode,
    initialImage: storedImage,
  });
  
  // Save image to session storage when it changes - debounced
  useEffect(() => {
    // Debounce to prevent excessive writes
    const saveTimeout = setTimeout(() => {
      if (typeof window !== 'undefined' && selectedImage && !sessionCleared) {
        try {
          sessionStorage.setItem(SESSION_IMAGE_KEY, selectedImage);
        } catch (error) {
          // Silent fail - image will be lost on refresh
        }
      }
    }, 300); // Debounce for 300ms
    
    return () => clearTimeout(saveTimeout);
  }, [selectedImage, sessionCleared]);

  // Handle mouse movement over the canvas
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || extractionMethod !== "manual") return

    const canvas = canvasRef.current
    const rect = canvas.getBoundingClientRect()
    
    // Calculate the cursor position relative to the canvas
    const x = Math.min(Math.max(0, e.clientX - rect.left), canvas.width - 1)
    const y = Math.min(Math.max(0, e.clientY - rect.top), canvas.height - 1)
    
    setMousePosition({ x, y })
    
    // Get the pixel color under the cursor
    try {
      const ctx = canvas.getContext("2d")
      if (ctx) {
        const pixelData = ctx.getImageData(x, y, 1, 1).data
        const pixelColor = rgbToHex(pixelData[0], pixelData[1], pixelData[2])
        setCurrentPixelColor(pixelColor)
      }
    } catch (error) {
      // Silent fail - color picker will not show current color
    }
  }

  // Calculate magnifier position to keep it within viewport bounds
  const getMagnifierPosition = (x: number, y: number, canvasRect: DOMRect, size: number) => {
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    // Default position (to the right of cursor)
    let posX = x + 20
    let posY = y - 75
    
    // Adjust if too close to right edge
    if (posX + size > viewportWidth - 20) {
      posX = x - size - 20 // Position to the left of cursor
    }
    
    // Adjust if too close to bottom edge
    if (posY + size > viewportHeight - 20) {
      posY = viewportHeight - size - 20
    }
    
    // Adjust if too close to top edge
    if (posY < 20) {
      posY = 20
    }
    
    return { left: posX, top: posY }
  }

  // Handle mouse enter on canvas
  const handleMouseEnter = () => {
    if (extractionMethod === "manual" && isPickingColor && (isAdminMode || creditState.credits > 0)) {
      setShowMagnifier(true)
    }
  }

  // Handle mouse leave on canvas
  const handleMouseLeave = () => {
    setShowMagnifier(false)
    setMousePosition(null)
    setCurrentPixelColor("")
  }

  // Get all colors for display and export - memoized to prevent recalculations
  const allColors = useMemo(() => extractedColors, [extractedColors])

  // Trigger file input click
  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  // Handle file change with additional security checks
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Check file size (limit to 5MB)
      const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
      if (file.size > MAX_FILE_SIZE) {
        toast({
          title: "File too large",
          description: "The image must be smaller than 5MB",
          variant: "destructive"
        })
        return
      }
      
      // Check file type - expanded list of supported formats
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/gif',
        'image/bmp',
        'image/tiff',
        'image/svg+xml',
        'image/x-icon',
        'image/vnd.microsoft.icon',
        'image/heic',
        'image/heif',
        'image/avif',
        'image/jp2',
        'image/jpx',
        'image/jpm',
        'image/jxl'
      ];
      
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Invalid file type",
          description: "Please upload a supported image format (JPEG, PNG, WebP, GIF, BMP, TIFF, SVG, ICO, HEIC, AVIF, JPEG2000, JPEG XL)",
          variant: "destructive"
        })
        return
      }

      // Validate image dimensions
      const img = new Image();
      img.onload = () => {
        const MAX_DIMENSION = 4096; // Maximum width or height
        if (img.width > MAX_DIMENSION || img.height > MAX_DIMENSION) {
          toast({
            title: "Image too large",
            description: "Image dimensions must be less than 4096x4096 pixels",
            variant: "destructive"
          });
          return;
        }
        
        const selectedFile = handleFileSelect(file);
        if (selectedFile) {
          setSelectedFile(selectedFile);
        }
      };
      
      img.onerror = () => {
        toast({
          title: "Invalid image",
          description: "The file appears to be corrupted or invalid",
          variant: "destructive"
        });
      };
      
      img.src = URL.createObjectURL(file);
    }
  }

  // Name colors using AI
  const nameColorsWithAI = async (hexColors: string[]): Promise<{hex: string, name: string}[]> => {
    try {
      const response = await fetch("/api/name-colors", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ colors: hexColors }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to name colors");
      }
      
      const data = await response.json();
      return data.colors;
    } catch (error) {
      // Fallback to local naming if AI fails
      return hexColors.map(hex => ({ 
        hex, 
        name: getColorName(hex) 
      }));
    }
  }

  // Handle starting color extraction with the selected method
  const handleExtract = async () => {
    if (selectedFile) {
      // Force refresh credit state before checking
      const freshState = updateCreditState(true);
      
      // Check if we have credits available or are in admin mode
      if (isAdminMode || freshState.credits > 0) {
        // For AI extraction, we'll consume 1 credit for up to 5 colors
        if (extractionMethod === "ai" && !isAdminMode) {
          // Always consume a credit BEFORE extraction for AI method
          const creditConsumed = consumeCredit();
          
          if (!creditConsumed) {
            toast({
              title: "Error",
              description: "Could not process credit. Please try again.",
              variant: "destructive",
              duration: 3000,
            });
            return;
          }
          
          // Show a toast notification about AI extracting colors
          toast({
            title: "AI is analyzing your image",
            description: "Extracting the most prominent and visually important colors...",
            duration: 5000,
          });
          
          await extractColors(selectedFile, extractionMethod);
        } else {
          await extractColors(selectedFile, extractionMethod);
        }
      } else {
        // If no credits, show toast with info about regeneration
        toast({
          title: "No credits available",
          description: (
            <div className="flex flex-col gap-2">
              <p>Credits will be available at {timeRemaining}.</p>
              <div className="text-xs mt-1 cursor-pointer text-blue-500" onClick={toggleAdminInput}>
                Upgrade for unlimited access
              </div>
            </div>
          ),
          duration: 5000,
        });
      }
    } else {
      toast({
        title: "No image selected",
        description: "Please upload an image first.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  // Extract a single color from the canvas when clicked
  const handleCanvasClick = async (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;

    // Check if we're in manual mode, if not, don't do anything
    if (extractionMethod !== "manual") return;

    // Check if we have credits available or are in admin mode
    if (!isAdminMode && creditState.credits === 0) {
      toast({
        title: "No credits available",
        description: (
          <div className="flex flex-col gap-2">
            <p>Credits will be available at {timeRemaining}.</p>
            <div className="text-xs mt-1 cursor-pointer text-blue-500" onClick={toggleAdminInput}>
              Upgrade for unlimited access
            </div>
          </div>
        ),
        duration: 5000,
      });
      return;
    }

    try {
      const canvas = canvasRef.current;
      const rect = canvas.getBoundingClientRect();
      const x = Math.min(Math.max(0, e.clientX - rect.left), canvas.width - 1);
      const y = Math.min(Math.max(0, e.clientY - rect.top), canvas.height - 1);

      const ctx = canvas.getContext("2d");
      if (ctx) {
        const pixelData = ctx.getImageData(x, y, 1, 1).data;
        const hex = rgbToHex(pixelData[0], pixelData[1], pixelData[2]);

        // Check if this color already exists
        if (extractedColors.some(color => color.hex.toLowerCase() === hex.toLowerCase())) {
          toast({
            title: "Duplicate color",
            description: (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: hex }}></div>
                <span>This color is already in your palette</span>
              </div>
            ),
            duration: 3000,
          });
          return;
        }

        // For manual picking, only consume a credit every COLORS_PER_CREDIT colors
        if (!isAdminMode) {
          const newPicksCount = manualPicksCount + 1;
          setManualPicksCount(newPicksCount);
          
          // If we've reached the threshold, consume a credit and reset the counter
          if (newPicksCount >= COLORS_PER_CREDIT) {
            const creditConsumed = consumeCredit();
            
            if (!creditConsumed) {
              toast({
                title: "Credit deduction failed",
                description: "Could not deduct credit. Please try again.",
                variant: "destructive",
                duration: 3000,
              });
              return;
            }
            
            setManualPicksCount(0);
            
            // Credit usage toast is now handled in consumeCredit function
          } else {
            // Show remaining picks
            toast({
              title: "Color extracted!",
              description: (
                <div className="flex flex-col gap-1">
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: hex }}></div>
                    <span>{hex}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {COLORS_PER_CREDIT - newPicksCount} more picks until 1 credit is used
                  </p>
                </div>
              ),
              duration: 2000,
            });
          }
        }

        // Set a loading state
        const tempColorId = Date.now().toString();
        const tempColor = { name: "Naming...", hex, id: tempColorId };
        setExtractedColors((prev) => [...prev, tempColor]);
        
        // Get color name from AI
        try {
          const namedColors = await nameColorsWithAI([hex]);
          if (namedColors && namedColors.length > 0) {
            // Update the color with the AI-generated name
            setExtractedColors((prev) => 
              prev.map(color => 
                color.id === tempColorId 
                  ? { ...namedColors[0], id: tempColorId } 
                  : color
              )
            );
            
            // Only show toast if we're in admin mode (otherwise it's shown in the credit handling code)
            if (isAdminMode) {
              toast({
                title: "Color extracted!",
                description: (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: hex }}></div>
                    <span>{namedColors[0].name} ({hex})</span>
                  </div>
                ),
                duration: 3000,
              });
            }
          }
        } catch (error) {
          console.error("Error getting color name:", error);
          // If AI naming fails, use the local getColorName function as fallback
          const colorName = getColorName(hex);
          setExtractedColors((prev) => 
            prev.map(color => 
              color.id === tempColorId 
                ? { name: colorName, hex, id: tempColorId } 
                : color
            )
          );
          
          // Only show toast if we're in admin mode (otherwise it's shown in the credit handling code)
          if (isAdminMode) {
            toast({
              title: "Color extracted!",
              description: (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: hex }}></div>
                  <span>{colorName} ({hex})</span>
                </div>
              ),
              duration: 3000,
            });
          }
        }
      }
    } catch (error) {
      console.error("Error extracting color:", error);
      toast({
        title: "Error extracting color",
        description: "Could not read pixel data from the image",
        variant: "destructive"
      });
    }
  };

  // Copy color to clipboard with error handling
  const copyColor = (hex: string) => {
    try {
      navigator.clipboard.writeText(hex).then(() => {
        setCopiedColor(hex)
        toast({
          title: "Color copied!",
          description: `${hex} has been copied to clipboard.`,
          duration: 2000,
        })

        // Reset copied state after 2 seconds
        setTimeout(() => {
          setCopiedColor(null)
        }, 2000)
      }).catch(() => {
        toast({
          title: "Failed to copy",
          description: "Could not copy to clipboard. Try again or copy manually.",
          variant: "destructive"
        })
      })
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Could not copy to clipboard. Try again or copy manually.",
        variant: "destructive"
      })
    }
  }

  // Show code preview 
  const handleShowCodePreview = () => {
    if (allColors.length === 0) {
      toast({
        title: "No colors to export",
        description: "Extract some colors first before viewing code.",
        variant: "destructive"
      })
      return
    }
    
    // Color codes should always be available to view (not premium)
    setShowCodePreview(true)
  }

  // Export palette as a file
  const exportPalette = () => {
    if (allColors.length === 0) {
      toast({
        title: "No colors to export",
        description: "Extract some colors first before exporting.",
        variant: "destructive"
      })
      return
    }
    
    try {
      const jsonContent = JSON.stringify(allColors, null, 2)
      const blob = new Blob([jsonContent], { type: "application/json" })
      const url = URL.createObjectURL(blob)

      const a = document.createElement("a")
      a.href = url
      a.download = "color-palette.json"
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: "Palette exported!",
        description: "Color palette has been exported as JSON file.",
        duration: 3000,
      })
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Could not export the color palette. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Remove a color from extracted colors
  const removeColor = (index: number) => {
    setExtractedColors(prev => prev.filter((_, i) => i !== index))
    toast({
      title: "Color removed",
      description: "The color has been removed from your palette.",
      duration: 2000,
    })
  }

  // Clear all extracted colors
  const clearAllColors = () => {
    if (extractedColors.length === 0) return
    
    setExtractedColors([])
    // Clear colors from session storage
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(SESSION_COLORS_KEY);
    }
    setSessionCleared(true);
    
    toast({
      title: "Colors cleared",
      description: "All extracted colors have been cleared.",
      duration: 2000,
    })
  }

  // Remove the image
  const removeImage = () => {
    setSelectedFile(null);
    setSelectedImage(null);
    // Clear image from session storage
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(SESSION_IMAGE_KEY);
    }
    setSessionCleared(true);
    
    toast({
      title: "Image removed",
      description: "The image has been removed.",
      duration: 2000,
    })
  }

  // Toggle the color picker with magnifier setup
  const handleToggleColorPicker = () => {
    const newState = !isPickingColor
    toggleColorPicker()
    
    // If we're turning off the color picker, hide magnifier
    if (!newState) {
      setShowMagnifier(false)
    }
  }

  // Handle extraction method change
  const handleExtractionMethodChange = (value: string) => {
    const method = value as ExtractionMethod
    setExtractionMethod(method)
    
    if (method === "manual") {
      // Always enable color picking mode when switching to manual
      if (!isPickingColor) {
        toggleColorPicker();
      }
      
      toast({
        title: "Manual mode activated",
        description: (
          <div className="flex items-center gap-2">
            <Pipette className="h-4 w-4" />
            <span>Click anywhere on the image to pick colors</span>
          </div>
        ),
        duration: 3000
      })
    } else {
      // When switching to AI mode, hide magnifier and disable color picking if active
      setShowMagnifier(false)
      if (isPickingColor) {
        toggleColorPicker();
      }
    }
  }

  // Verify admin code
  const verifyAdminCode = (code: string) => {
    if (code === ADMIN_CODE) {
      setIsAdminMode(true)
      setShowAdminInput(false)
      // Reset credits for admin (unlimited)
      resetCredits()
      updateCreditState()
      toast({
        title: "Admin mode activated",
        description: "No color extraction limits applied",
        duration: 3000
      })
    } else {
      toast({
        title: "Invalid code",
        description: "The code entered is not valid",
        variant: "destructive",
        duration: 3000
      })
    }
    setAdminCodeInput("")
  }

  // Toggle admin input visibility
  const toggleAdminInput = () => {
    setShowAdminInput(prev => !prev)
  }

  return (
    <TooltipProvider>
      <div className="fixed inset-0 flex flex-col bg-background overflow-hidden">
        {/* App header */}
        <header className="border-b bg-card p-4 flex items-center justify-between shadow-sm">
          <div className="flex items-center gap-2">
            {/* Logo - Always visible */}
            <div className="flex items-center gap-2 ml-2">
              <div className="relative">
                <LayoutPanelTop className="h-6 w-6 text-transparent" />
                <div className="absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md" style={{maskImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'currentColor\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121\'/%3E%3C/svg%3E")', maskSize: 'cover'}} />
              </div>
              <h1 className="text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400" style={{fontFamily: "var(--font-montserrat)", letterSpacing: "-0.5px", fontWeight: "800"}}>
               <Link href="/" data-barba="wrapper">Coloriqo</Link>
                </h1>
            </div>
            {isAdminMode && (
              <div className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full font-medium">
                Admin
              </div>
            )}
          </div>
          {/* Desktop Layout */}
          <div className="hidden md:flex items-center gap-4">
            {/* Credits display */}
            <div className="flex items-center gap-2 px-3 py-1 border rounded-md bg-background">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">
                {isAdminMode ? "∞" : creditState.credits} Credits
              </span>
              {creditState.credits === 0 && !isAdminMode && timeRemaining && (
                <div className="flex items-center text-xs text-muted-foreground ml-1">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>Available at {timeRemaining}</span>
                </div>
              )}
              {!isAdminMode && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 ml-1 px-2 text-xs text-blue-500"
                  onClick={showCreditStatus}
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Status
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <ThemeSwitcher />
              {isAdminMode ? (
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-200"
                  onClick={() => {
                    setIsAdminMode(false);
                    toast({
                      title: "Logged out",
                      description: "Returned to standard user mode",
                      duration: 3000
                    });
                  }}
                >
                  <LogOut className="mr-2 h-4 w-4" /> Admin Logout
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleAdminInput}
                >
                  <LogIn className="mr-2 h-4 w-4" /> Admin Login
                </Button>
              )}
              {/* Server-side rendering safe buttons with consistent disabled state */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleShowCodePreview}
                disabled={extractedColors.length === 0 ? true : false}
              >
                <Code className="mr-2 h-4 w-4" /> View Code
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={exportPalette}
                disabled={extractedColors.length === 0 ? true : false}
              >
                <Download className="mr-2 h-4 w-4" /> Export
              </Button>
              {extractedColors.length > 0 ? (
                <Button variant="outline" size="sm" onClick={clearAllColors}>
                  <Trash2 className="mr-2 h-4 w-4" /> Clear All
                </Button>
              ) : null}
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="flex md:hidden items-center gap-2">
            {/* Credits display - Always visible on mobile */}
            <div className="flex items-center gap-1 px-2 py-1 border rounded-md bg-background text-xs">
              <CreditCard className="h-3 w-3 text-muted-foreground" />
              <span className="font-medium">
                {isAdminMode ? "∞" : creditState.credits}
              </span>
            </div>

            {/* Theme Switcher - Always visible on mobile */}
            <ThemeSwitcher />

            {/* Hamburger Menu Button */}
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle navigation menu"
              aria-expanded={isMobileMenuOpen}
            >
              <div className="flex flex-col justify-center items-center w-4 h-4">
                <span className={`block h-0.5 w-4 bg-current transition-all duration-300 ${isMobileMenuOpen ? 'rotate-45 translate-y-1' : ''}`} />
                <span className={`block h-0.5 w-4 bg-current transition-all duration-300 mt-1 ${isMobileMenuOpen ? 'opacity-0' : ''}`} />
                <span className={`block h-0.5 w-4 bg-current transition-all duration-300 mt-1 ${isMobileMenuOpen ? '-rotate-45 -translate-y-1' : ''}`} />
              </div>
            </Button>
          </div>

          {/* Mobile Menu Overlay - Slides from right to left */}
          <div className={`md:hidden fixed inset-0 z-50 transition-all duration-300 ${isMobileMenuOpen ? 'visible' : 'invisible'}`}>
            {/* Background overlay */}
            <div
              className={`absolute inset-0 bg-black/50 transition-opacity duration-300 ${isMobileMenuOpen ? 'opacity-100' : 'opacity-0'}`}
              onClick={() => setIsMobileMenuOpen(false)}
            />

            {/* Menu panel sliding from right */}
            <div className={`absolute right-0 top-0 h-full w-80 bg-background border-l shadow-lg transform transition-transform duration-300 ${isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}`}>
              {/* Menu header */}
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-lg font-semibold">Tools</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setIsMobileMenuOpen(false)}
                  aria-label="Close menu"
                >
                  <span className="block h-0.5 w-4 bg-current rotate-45" />
                  <span className="block h-0.5 w-4 bg-current -rotate-45 -translate-y-0.5" />
                </Button>
              </div>

              {/* Menu content */}
              <div className="flex flex-col p-4 space-y-4">
                {/* Admin Section */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Admin</h3>
                  {isAdminMode ? (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-200"
                      onClick={() => {
                        setIsAdminMode(false);
                        setIsMobileMenuOpen(false);
                        toast({
                          title: "Logged out",
                          description: "Returned to standard user mode",
                          duration: 3000
                        });
                      }}
                    >
                      <LogOut className="mr-2 h-4 w-4" /> Admin Logout
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => {
                        toggleAdminInput();
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      <LogIn className="mr-2 h-4 w-4" /> Admin Login
                    </Button>
                  )}
                </div>

                {/* Tools Section */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Tools</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      handleShowCodePreview();
                      setIsMobileMenuOpen(false);
                    }}
                    disabled={extractedColors.length === 0 ? true : false}
                  >
                    <Code className="mr-2 h-4 w-4" /> View Code
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      exportPalette();
                      setIsMobileMenuOpen(false);
                    }}
                    disabled={extractedColors.length === 0 ? true : false}
                  >
                    <Download className="mr-2 h-4 w-4" /> Export
                  </Button>
                  {extractedColors.length > 0 ? (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => {
                        clearAllColors();
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      <Trash2 className="mr-2 h-4 w-4" /> Clear All
                    </Button>
                  ) : null}
                </div>

                {/* Credits Section */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Credits</h3>
                  <div className="flex items-center gap-2 px-3 py-2 border rounded-md bg-background">
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">
                      {isAdminMode ? "∞" : creditState.credits} Credits
                    </span>
                  </div>
                  {creditState.credits === 0 && !isAdminMode && timeRemaining && (
                    <div className="flex items-center text-xs text-muted-foreground px-3 py-2 border rounded-md bg-muted/50">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>Available at {timeRemaining}</span>
                    </div>
                  )}
                  {!isAdminMode && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full text-blue-500"
                      onClick={() => {
                        showCreditStatus();
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Check Status
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </header>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <aside className="w-64 border-r bg-muted/40 p-4 flex flex-col overflow-y-auto">
            <Tabs defaultValue="upload" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="w-full">
                <TabsTrigger value="upload" className="flex-1">Upload</TabsTrigger>
                <TabsTrigger value="palette" className="flex-1">Palette</TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="mt-4">
                <div className="space-y-4">
                  <Button onClick={handleUploadClick} variant="default" className="w-full">
                    <Upload className="mr-2 h-4 w-4" /> Upload Image
                  </Button>
                  <input 
                    type="file" 
                    ref={fileInputRef} 
                    onChange={handleFileChange} 
                    accept="image/jpeg,image/png,image/webp,image/gif,image/bmp,image/tiff,image/svg+xml,image/x-icon,image/vnd.microsoft.icon,image/heic,image/heif,image/avif,image/jp2,image/jpx,image/jpm,image/jxl" 
                    className="hidden" 
                  />

                  {selectedImage && (
                    <div className="space-y-4">
                      <Separator />
                      <p className="text-sm font-medium">Extraction Method</p>
                      
                      <Tabs defaultValue="ai" className="w-full" onValueChange={(value) => handleExtractionMethodChange(value)}>
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="ai" className="flex items-center">
                            <Cpu className="mr-2 h-4 w-4" /> AI
                          </TabsTrigger>
                          <TabsTrigger value="manual" className="flex items-center">
                            <Hand className="mr-2 h-4 w-4" /> Manual
                          </TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="ai" className="mt-2">
                          <p className="text-xs text-muted-foreground mb-2">
                            Extract multiple colors at once using AI.
                          </p>
                          {creditState.credits === 0 && !isAdminMode ? (
                            <div className="p-3 border border-dashed rounded-md bg-muted/50">
                              <div className="flex flex-col items-center mb-3">
                                <RefreshCw className="h-5 w-5 text-muted-foreground mb-2 animate-spin" />
                                <p className="text-sm text-center mb-1">Out of credits</p>
                                {timeRemaining && (
                                  <p className="text-xs text-muted-foreground flex items-center">
                                    <Clock className="h-3 w-3 mr-1" />
                                    Credits at {timeRemaining}
                                  </p>
                                )}
                              </div>
                              <div className="flex justify-between items-center gap-2">
                                <p className="text-xs text-muted-foreground">
                                  Credits refill every {getCreditRegenerationPeriod()}
                                </p>
                                <Button 
                                  onClick={toggleAdminInput}
                                  size="sm"
                                  variant="outline"
                                  className="whitespace-nowrap"
                                >
                                  <CreditCard className="mr-2 h-3 w-3" /> Upgrade
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <Button 
                              onClick={handleExtract} 
                              disabled={isExtracting || !selectedFile}
                              className="w-full"
                              size="sm"
                            >
                              {isExtracting ? (
                                <>
                                  <Loader size="sm" className="mr-2" /> Extracting...
                                </>
                              ) : (
                                <>
                                  <Cpu className="mr-2 h-4 w-4" /> Extract Colors
                                </>
                              )}
                            </Button>
                          )}
                          {extractionError && (
                            <p className="text-xs text-red-500 mt-2">{extractionError}</p>
                          )}
                        </TabsContent>
                        
                        <TabsContent value="manual" className="mt-2">
                          <div className="space-y-3">
                            {creditState.credits === 0 && !isAdminMode ? (
                              <div className="p-3 border border-dashed rounded-md bg-muted/50">
                                <div className="flex flex-col items-center mb-3">
                                  <RefreshCw className="h-5 w-5 text-muted-foreground mb-2 animate-spin" />
                                  <p className="text-sm text-center mb-1">Out of credits</p>
                                  {timeRemaining && (
                                    <p className="text-xs text-muted-foreground flex items-center">
                                      <Clock className="h-3 w-3 mr-1" />
                                      Credits at {timeRemaining}
                                    </p>
                                  )}
                                </div>
                                <div className="flex justify-between items-center gap-2">
                                  <p className="text-xs text-muted-foreground">
                                    Credits refill every {getCreditRegenerationPeriod()}
                                  </p>
                                  <Button 
                                    onClick={toggleAdminInput}
                                    size="sm"
                                    variant="outline"
                                    className="whitespace-nowrap"
                                  >
                                    <CreditCard className="mr-2 h-3 w-3" /> Upgrade
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <>
                                <div className="flex items-center">
                                  <div className={`w-3 h-3 rounded-full mr-2 bg-green-500`}></div>
                                  <p className="text-xs font-medium">
                                    Ready to pick colors
                                  </p>
                                </div>

                                <p className="text-xs text-muted-foreground">
                                  Hover over the image to see a magnified view, then click to extract any color you want.
                                </p>
                                
                                {currentPixelColor && (
                                  <div className="p-2 border rounded-md bg-card">
                                    <p className="text-xs mb-1 text-muted-foreground">Current color:</p>
                                    <div className="flex items-center space-x-2">
                                      <div 
                                        className="w-8 h-8 rounded-md border" 
                                        style={{ backgroundColor: currentPixelColor }}
                                      />
                                      <span className="text-xs font-mono">{currentPixelColor}</span>
                                    </div>
                                  </div>
                                )}
                                
                                <div className="border-t pt-2 mt-2">
                                  <p className="text-[10px] text-muted-foreground">
                                    <span className="font-medium">Tip:</span> You can pick multiple colors without having to reselect this tab each time.
                                  </p>
                                </div>
                              </>
                            )}
                          </div>
                        </TabsContent>
                      </Tabs>

                      {imageSize && (
                        <div className="mt-2">
                          <p className="text-xs text-muted-foreground">
                            Image size: {Math.round(imageSize.width)} × {Math.round(imageSize.height)}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="palette" className="mt-4">
                <div className="space-y-4">
                  {extractedColors.length > 0 ? (
                    <>
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Extracted Colors</p>
                        <div className="flex items-center gap-2">
                          {!isAdminMode && (
                            <span className="text-xs text-muted-foreground">
                              {extractedColors.length}/{COLOR_LIMIT_STANDARD}
                            </span>
                          )}
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-6 w-6" 
                            onClick={clearAllColors}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2 max-h-[500px] overflow-y-auto pr-1">
                        {extractedColors.map((color, index) => (
                          <div key={index} className="flex items-center group">
                            <div 
                              className="h-8 w-8 rounded-md mr-2 border" 
                              style={{ backgroundColor: color.hex }}
                            />
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">{color.name}</p>
                              <p className="text-xs text-muted-foreground">{color.hex}</p>
                            </div>
                            <div className="flex">
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8" 
                                onClick={() => copyColor(color.hex)}
                              >
                                {copiedColor === color.hex ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                              </Button>
                              <Button
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity" 
                                onClick={() => removeColor(index)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="pt-2">
                        <Button variant="outline" size="sm" className="w-full" onClick={handleShowCodePreview}>
                          <Code className="mr-2 h-4 w-4" /> View Code
                        </Button>
                      </div>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-8 text-center text-muted-foreground">
                      <Pipette className="h-12 w-12 mb-4 opacity-20" />
                      <p className="mb-2">No colors extracted yet</p>
                      <p className="text-xs">Upload an image and use AI or the color picker to extract colors</p>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </aside>

          {/* Main content */}
          <main className="flex-1 p-6 overflow-y-auto">
            {selectedImage ? (
              <div className="relative flex justify-center">
                  <canvas
                    ref={canvasRef}
                    onClick={handleCanvasClick}
                  onMouseMove={handleMouseMove}
                  onMouseEnter={handleMouseEnter}
                  onMouseLeave={handleMouseLeave}
                  className={`border border-border rounded-lg max-w-full shadow-md ${isPickingColor ? "cursor-crosshair" : ""}`}
                  style={{ maxHeight: "70vh" }}
                  />
                  
                  {/* Remove image button */}
                  <Button 
                    variant="destructive" 
                    size="icon" 
                    className="absolute top-2 right-2 h-8 w-8 shadow-md hover:bg-red-600 z-10" 
                    onClick={removeImage}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                  
                  {isPickingColor && (
                  <div className="absolute top-3 left-3 bg-background/90 text-foreground px-3 py-1.5 rounded-md text-sm shadow-xl border border-border">
                      Click anywhere on the image to extract a color
                    </div>
                  )}
                {isPickingColor && showMagnifier && mousePosition && canvasRef.current && (
                  <div 
                    className="pointer-events-none fixed"
                    style={{ 
                      position: 'absolute',
                      left: getMagnifierPosition(mousePosition.x, mousePosition.y, canvasRef.current.getBoundingClientRect(), 150).left, 
                      top: getMagnifierPosition(mousePosition.x, mousePosition.y, canvasRef.current.getBoundingClientRect(), 150).top,
                      zIndex: 50
                    }}
                  >
                    <Magnifier 
                      sourceCanvas={canvasRef.current} 
                      x={mousePosition.x} 
                      y={mousePosition.y} 
                      zoomLevel={5}
                      size={150}
                    />
                  </div>
                )}
                
                {/* AI extraction overlay */}
                {isExtracting && extractionMethod === "ai" && (
                  <div className="absolute inset-0 bg-background/70 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg border border-border">
                    <div className="relative">
                      <Cpu className="h-12 w-12 text-primary animate-pulse mb-4" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="h-6 w-6 rounded-full border-2 border-primary border-t-transparent animate-spin"></div>
                      </div>
                    </div>
                    <h3 className="text-lg font-medium mb-2">AI Analyzing Image</h3>
                    <p className="text-center text-muted-foreground mb-4 max-w-md px-4">
                      Extracting the most prominent and visually important colors in your image...
                    </p>
                  </div>
                )}
                
                {creditState.credits === 0 && !isAdminMode && (
                  <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg border border-border">
                    <RefreshCw className="h-12 w-12 text-muted-foreground mb-4 animate-spin" />
                    <h3 className="text-lg font-medium mb-2">Credits Depleted</h3>
                    {timeRemaining && (
                      <div className="flex items-center gap-2 text-muted-foreground mb-2">
                        <Clock className="h-4 w-4" />
                        <span>Credits available at {timeRemaining}</span>
                      </div>
                    )}
                    <p className="text-center text-muted-foreground mb-4 max-w-md px-4">
                      Credits automatically refill every {getCreditRegenerationPeriod()}, or you can upgrade now for unlimited access.
                    </p>
                    <div className="flex gap-3">
                      <Button variant="outline" onClick={showCreditStatus}>
                        <RefreshCw className="mr-2 h-4 w-4" /> Check Status
                      </Button>
                      <Button onClick={toggleAdminInput}>
                        <CreditCard className="mr-2 h-4 w-4" /> Upgrade Now
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <div className="p-12 border-2 border-dashed border-border rounded-lg">
                  <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Image Selected</h3>
                  <p className="text-muted-foreground mb-4 max-w-md">
                    Upload an image to extract colors using AI or manually pick colors with the eyedropper tool.
                  </p>
                  <Button onClick={() => { 
                    handleUploadClick();
                    setActiveTab("upload");
                  }}>
                    <Upload className="mr-2 h-4 w-4" /> Upload Image
                  </Button>
                </div>
              </div>
            )}

            {/* Extracted color palette display */}
        {extractedColors.length > 0 && (
              <div className="mt-6">
                <h2 className="text-lg font-semibold mb-4">Extracted Color Palette</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {extractedColors.map((color, index) => (
                    <Card key={index} className="overflow-hidden border">
                  <div
                        className="h-28"
                    style={{ backgroundColor: color.hex }}
                      />
                  <CardContent className="p-4">
                        <div className="flex justify-between items-center">
                          <div>
                    <p className="font-medium">{color.name}</p>
                            <p className="text-sm text-muted-foreground">{color.hex}</p>
                          </div>
                          <div className="flex gap-1">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" onClick={() => copyColor(color.hex)}>
                                  {copiedColor === color.hex ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent side="left">
                                <p>Copy color</p>
                              </TooltipContent>
                            </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" onClick={() => removeColor(index)}>
                                  <Trash2 className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                              <TooltipContent side="left">
                                <p>Remove color</p>
                      </TooltipContent>
                    </Tooltip>
                          </div>
                        </div>
                      </CardContent>
                </Card>
              ))}
                </div>
              </div>
            )}
          </main>
        </div>

        {/* App footer */}
        <footer className="border-t p-2 text-center text-xs text-muted-foreground">
          Coloriqo - The right color tool
        </footer>

        {/* Code preview dialog */}
        {showCodePreview && (
          <CodePreview 
            colors={allColors} 
            onClose={() => setShowCodePreview(false)} 
          />
        )}

        {/* Admin code input dialog */}
        {showAdminInput && (
          <div 
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center"
            onClick={(e) => {
              // Close dialog when clicking on backdrop (outside the modal)
              if (e.target === e.currentTarget) {
                setShowAdminInput(false);
              }
            }}
          >
            <div className="bg-card border rounded-lg shadow-lg w-96 p-6">
              <h3 className="text-lg font-semibold mb-4">Enter Admin Code</h3>
              <div className="space-y-4">
                <input
                  type="password"
                  value={adminCodeInput}
                  onChange={(e) => setAdminCodeInput(e.target.value)}
                  placeholder="Enter admin code..."
                  className="w-full p-2 border rounded-md bg-background text-foreground"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      verifyAdminCode(adminCodeInput);
                    }
                  }}
                />
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowAdminInput(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => verifyAdminCode(adminCodeInput)}
                  >
                    Submit
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  )
}
