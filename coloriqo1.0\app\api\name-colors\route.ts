import { NextRequest, NextResponse } from "next/server";
import { generateColorNamingPrompt, parseColorsFromResponse } from "@/lib/color-service";

// Maximum number of colors that can be named at once
const MAX_COLORS = 20;

export async function POST(req: NextRequest) {
  try {
    const { colors } = await req.json();
    
    // Validate input
    if (!colors || !Array.isArray(colors) || colors.length === 0) {
      return NextResponse.json(
        { error: "No colors provided" },
        { status: 400 }
      );
    }

    // Validate array length
    if (colors.length > MAX_COLORS) {
      return NextResponse.json(
        { error: "Too many colors" },
        { status: 400 }
      );
    }

    // Validate each color
    if (!colors.every(color => typeof color === 'string' && /^#[0-9A-Fa-f]{6}$/.test(color))) {
      return NextResponse.json(
        { error: "Invalid color format" },
        { status: 400 }
      );
    }

    // Get API key from environment variables
    const apiKey = process.env.GEMINI_API_KEY;
    
    if (!apiKey) {
      return NextResponse.json(
        { error: "Service temporarily unavailable" },
        { status: 503 }
      );
    }

    // Generate a prompt for naming colors
    const prompt = generateColorNamingPrompt(colors);

    // Make request to Gemini API
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.2,
            maxOutputTokens: 1024
          }
        }),
      }
    );

    if (!response.ok) {
      return NextResponse.json(
        { error: "Failed to process colors" },
        { status: 500 }
      );
    }

    const data = await response.json();
    
    // Parse the response to extract the colors JSON
    const namedColorsText = data.candidates[0]?.content?.parts[0]?.text || "";
    
    try {
      // Parse the colors from the response
      const namedColors = await parseColorsFromResponse(namedColorsText);
      
      return NextResponse.json({ colors: namedColors });
    } catch (e) {
      return NextResponse.json(
        { error: "Failed to process color names" },
        { status: 500 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
} 