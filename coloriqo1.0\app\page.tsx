"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ColorChangingLogo } from "@/components/ui/color-changing-logo"
import { FeatureCard } from "@/components/ui/feature-card"
import { GradientButton } from "@/components/ui/gradient-button"
import { ThemeSwitcher } from "@/components/ui/theme-switcher"
import { Cpu, Palette, Code, ArrowRight, MousePointerClick, Download, Star, ChevronRight, Copy, ArrowUp, LayoutPanelTop } from "lucide-react"
import Link from "next/link"


// Define features for the app
const FEATURES = [
  {
    icon: Cpu,
    title: "AI-Powered Extraction",
    description: "Extract colors intelligently using advanced AI algorithms for optimal palette generation.",
    iconColor: "bg-blue-100 text-blue-500 dark:bg-blue-900/50 dark:text-blue-400"
  },
  {
    icon: Palette,
    title: "Custom Color Naming",
    description: "Get semantic names for your colors that help communicate and organize your palette.",
    iconColor: "bg-purple-100 text-purple-500 dark:bg-purple-900/50 dark:text-purple-400"
  },
  {
    icon: MousePointerClick,
    title: "Precision Picking",
    description: "Pixel-perfect color selection with magnified precision tools.",
    iconColor: "bg-pink-100 text-pink-500 dark:bg-pink-900/50 dark:text-pink-400"
  },
  {
    icon: Code,
    title: "Export to Code",
    description: "Export your palette directly for immediate use.",
    iconColor: "bg-orange-100 text-orange-500 dark:bg-orange-900/50 dark:text-orange-400"
  },
  {
    icon: Copy,
    title: "Copy Formats",
    description: "Copy colors in HEX formats with a single click.",
    iconColor: "bg-emerald-100 text-emerald-500 dark:bg-emerald-900/50 dark:text-emerald-400"
  },
  {
    icon: Download,
    title: "One-Click Export",
    description: "Save your palettes in various formats for use in all major design tools.",
    iconColor: "bg-indigo-100 text-indigo-500 dark:bg-indigo-900/50 dark:text-indigo-400"
  }
];

// Testimonials data
const TESTIMONIALS = [
  {
    quote: "Coloriqo changed my workflow completely. I save hours on every project by extracting the perfect palette instantly.",
    name: "Sarah Johnson",
    title: "Senior UI Designer",
    avatar: "https://i.pravatar.cc/150?img=32"
  },
  {
    quote: "The AI color naming feature is brilliant. No more struggling to name shades in my design system documentation.",
    name: "Michael Torres",
    title: "Product Designer",
    avatar: "https://i.pravatar.cc/150?img=59"
  },
  {
    quote: "As a developer, the code export options are fantastic. Perfect integration with my CSS variables.",
    name: "Leila Khan",
    title: "Frontend Developer",
    avatar: "https://i.pravatar.cc/150?img=48"
  }
];

// Simple Floating Scroll To Top Button
function FloatingScrollButton() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      setIsVisible(window.scrollY > 300);
    };
    
    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  };

  return (
    <Button
      onClick={scrollToTop}
      className={`fixed right-8 top-1/2 z-50 rounded-full p-3 shadow-lg transition-all duration-500
        bg-gradient-to-r from-purple-500 to-pink-500 hover:from-pink-500 hover:to-purple-500
        border-2 border-white/20 backdrop-blur-sm scroll-to-top-btn
        ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-0 pointer-events-none'}`}
      size="icon"
      aria-label="Scroll to top"
    >
      <ArrowUp className="h-5 w-5 text-white" />
    </Button>
  );
}

export default function Home() {
  const [mounted, setMounted] = useState(false);
  const [activeTestimonial, setActiveTestimonial] = useState(0);
  const [isHoveringDemo, setIsHoveringDemo] = useState(false);
  
  useEffect(() => {
    setMounted(true);
    
    // Auto-rotate testimonials
    const interval = setInterval(() => {
      setActiveTestimonial(prev => (prev + 1) % TESTIMONIALS.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);
  
  if (!mounted) {
    return null;
  }

  return (
    <main className="min-h-screen bg-background font-sans antialiased relative overflow-hidden">
      {/* Background overlay */}
      <div className="pointer-events-none fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.08),transparent_70%)]" />
      
      {/* Top color bar */}
      <div className="h-1.5 w-full bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500" />
      
      {/* Main content */}
      <div className="relative">
        {/* Header */}
        <header className="container mx-auto px-4 py-6">
          <div className="flex justify-between items-center">

            <div className="hidden md:flex items-center gap-2 ml-2">
              <div className="relative">
                <LayoutPanelTop className="h-6 w-6 text-transparent" />
                <div className="absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md" style={{maskImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'currentColor\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121\'/%3E%3C/svg%3E")', maskSize: 'cover'}} />
              </div>
              <h1 className="text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400" style={{fontFamily: "var(--font-montserrat)", letterSpacing: "-0.5px", fontWeight: "800"}}>
               <Link href="/tool" data-barba="wrapper">Coloriqo</Link>
              </h1>
              
            </div>
            <div className="flex items-center gap-6">
              <nav className="hidden md:flex gap-8">
                <a href="#features" className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                  Features
                </a>
                <a href="#testimonials" className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                  Testimonials
                </a>
                <a href="#pricing" className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                  Pricing
                </a>
              </nav>
                <ThemeSwitcher />
              <Button size="sm">
                <Link href="/tool" data-barba="wrapper">Get Started</Link>
              </Button>
            </div>
          </div>
        </header>
        
        {/* Hero section */}
        <section className="py-20 md:py-28">
          <div className="container mx-auto px-4">
            <div className="flex flex-col items-center text-center max-w-5xl mx-auto">
              <div className="mb-6 inline-flex items-center rounded-full border border-neutral-200 dark:border-neutral-800 px-3 py-1 text-sm gap-1 bg-background shadow-sm">
                <span className="flex h-2 w-2 rounded-full bg-green-500"></span>
                <span className="text-muted-foreground">Now with AI-powered extraction</span>
        </div>
          
              <h1 className="text-4xl md:text-6xl font-heading font-bold tracking-tight mb-6">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 animate-gradient-x bg-size-200">
                  Transform Any Image
            </span>
                <br /> 
                Into the Perfect Color Palette
          </h1>
              
              <p className="text-xl text-muted-foreground max-w-3xl mb-10">
                Extract harmonious colors from any image with AI precision. 
                Build perfect palettes for your design projects in seconds, not hours.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-16">
                <GradientButton size="lg" className="px-8 py-6 font-medium text-base">
                  <Link href="/tool" className="flex items-center gap-2 justify-center" data-barba="wrapper">
                    Start extracting colors <ArrowRight size={16} />
                  </Link>
          </GradientButton>
                <Button variant="outline" size="lg" className="px-8 py-6">
                  <span className="flex items-center gap-2 justify-center">
                    <Link href = "#video">Watch demo</Link>
                  </span>
                </Button>
              </div>
              
              {/* App Preview */}
              <div className="relative w-full max-w-6xl perspective-1000">
                {/* Shadow */}
                <div className="absolute inset-y-4 -inset-x-4 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 rounded-xl blur-xl -z-10 transition-all duration-500"></div>
          
              {/* App mockup */}
                <div 
                  className="rounded-xl overflow-hidden border border-neutral-200 dark:border-neutral-800 bg-card shadow-2xl transform transition-all duration-500"
                  style={{
                    transform: isHoveringDemo ? 'rotateX(2deg) translateY(-4px)' : 'rotateX(0) translateY(0)',
                    boxShadow: isHoveringDemo ? '0 25px 50px -12px rgba(0, 0, 0, 0.08)' : '0 10px 30px -15px rgba(0, 0, 0, 0.08)'
                  }}
                  onMouseEnter={() => setIsHoveringDemo(true)}
                  onMouseLeave={() => setIsHoveringDemo(false)}
                >
                  {/* App header */}

                    
                <div className="flex justify-between items-center p-2 bg-muted/70 border-b border-neutral-200 dark:border-neutral-800">
                  <div className="flex items-center gap-1.5">
                  <div className="w-3 h-3 rounded-full bg-red-400"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                  <div className="w-3 h-3 rounded-full bg-green-400"></div>
                </div>
                    <div className="text-xs text-muted-foreground font-medium">Coloriqo - Color Extraction Tool</div>
                    <div className="w-16"></div> 

                </div>
                  
                  {/* App content */}
                  <div id = "video" className="scroll-mt-12">
                    {/* Video placeholder */}
                    <video
                      src="https://res.cloudinary.com/didt1ywys/video/upload/v1749389530/cursorful-video-1749385801624_jwfjsx.mp4" 
                      autoPlay
                      muted
                      playsInline
                      loop
                    /> 
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Features Section */}
        <section id="features" className="py-24 bg-muted/20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-heading font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-purple-500">
                  Powerful Features
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Everything you need to extract, refine, and utilize color palettes
              </p>
            </div>
          
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {FEATURES.map((feature, index) => (
              <FeatureCard 
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                iconColor={feature.iconColor}
                  className="transition-all duration-300 hover:shadow-md hover:-translate-y-1 border border-neutral-200 dark:border-neutral-800"
              />
            ))}
            </div>
          </div>
        </section>
        
        {/* Testimonials section */}
        <section id="testimonials" className="py-24">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-heading font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-pink-500 to-orange-500">
                  Loved by Designers & Developers
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                See why professionals choose Coloriqo for their color extraction needs
              </p>
            </div>
            
            <div className="max-w-4xl mx-auto relative">
              {/* Testimonial cards */}
              <div className="overflow-hidden relative h-72">
                {TESTIMONIALS.map((testimonial, index) => (
                  <div 
                    key={index}
                    className="absolute inset-0 transition-all duration-500 flex flex-col items-center justify-center p-8 rounded-xl border border-neutral-200 dark:border-neutral-800 bg-card"
                    style={{
                      opacity: index === activeTestimonial ? 1 : 0,
                      transform: `translateX(${(index - activeTestimonial) * 100}%)`,
                      zIndex: index === activeTestimonial ? 10 : 0
                    }}
                  >
                    <div className="mb-6">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star key={star} className="inline-block h-4 w-4 text-amber-400 fill-amber-400" />
                      ))}
                    </div>
                    <p className="text-xl font-medium text-center mb-6">"{testimonial.quote}"</p>
                    <div className="flex items-center">
                      <img src={testimonial.avatar} alt={testimonial.name} className="w-10 h-10 rounded-full mr-3" />
                      <div>
                        <p className="font-medium">{testimonial.name}</p>
                        <p className="text-sm text-muted-foreground">{testimonial.title}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Testimonial navigation dots */}
              <div className="flex justify-center gap-2 mt-8">
                {TESTIMONIALS.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveTestimonial(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === activeTestimonial 
                        ? 'bg-primary w-4' 
                        : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                    }`}
                    aria-label={`View testimonial ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>
        </section>
        
        {/* CTA section */}
        <section className="py-20 bg-muted/20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto rounded-2xl bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 p-8 md:p-12 border border-neutral-200 dark:border-neutral-800 text-center">
              <h3 className="text-3xl md:text-4xl font-heading font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500">
                  Ready to Transform Your Design Workflow?
                </span>
              </h3>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
                Join thousands of designers extracting perfect color palettes in seconds.
              </p>
              <GradientButton size="lg" className="px-8 py-6 font-medium text-base">
                <Link href="/tool" className="flex items-center gap-2 justify-center" data-barba="wrapper">
                  Start for free <ChevronRight size={16} className="animate-pulse" />
                </Link>
              </GradientButton>
              <p className="text-sm text-muted-foreground mt-4">
                No credit card required. 10 free extractions included.
              </p>
            </div>
          </div>
        </section>
        
        {/* Footer */}
        <footer className="py-12 border-t border-neutral-200 dark:border-neutral-800">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div>
                <div className="hidden md:flex items-center gap-2 ml-2">
              <div className="relative">
                <LayoutPanelTop className="h-6 w-6 text-transparent" />
                <div className="absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md" style={{maskImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'currentColor\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121\'/%3E%3C/svg%3E")', maskSize: 'cover'}} />
              </div>
              <h1 className="text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400" style={{fontFamily: "var(--font-montserrat)", letterSpacing: "-0.5px", fontWeight: "800"}}>
               <Link href="/tool" data-barba="wrapper">Coloriqo</Link>
              </h1>
              
            </div>
                <p className="text-sm text-muted-foreground mt-2">
                  AI-powered color extraction for perfect palettes.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium mb-3">Product</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li><a href="#" className="hover:text-foreground transition-colors">Features</a></li>
                  <li><a href="#" className="hover:text-foreground transition-colors">Pricing</a></li>
                  <li><a href="#" className="hover:text-foreground transition-colors">Changelog</a></li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-3">Resources</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li><a href="#" className="hover:text-foreground transition-colors">Documentation</a></li>
                  <li><a href="#" className="hover:text-foreground transition-colors">Tutorials</a></li>
                  <li><a href="#" className="hover:text-foreground transition-colors">Blog</a></li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-3">Company</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li><a href="#" className="hover:text-foreground transition-colors">About us</a></li>
                  <li><a href="#" className="hover:text-foreground transition-colors">Contact</a></li>
                  <li><a href="#" className="hover:text-foreground transition-colors">Privacy Policy</a></li>
                </ul>
              </div>
            </div>
            
            <div className="mt-12 pt-6 border-t border-neutral-200 dark:border-neutral-800 flex flex-col md:flex-row justify-between items-center">
              <p className="text-sm text-muted-foreground mb-4 md:mb-0">
              © {new Date().getFullYear()} Coloriqo. All rights reserved.
            </p>
              <div className="flex space-x-6">
                <a href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334q0-.265-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15"/>
                  </svg>
                </a>
                <a href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8"/>
                  </svg>
                </a>
                <a href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </footer>
      </div>
      
      {/* Add the floating scroll button */}
      <FloatingScrollButton />
    </main>
  )
}
