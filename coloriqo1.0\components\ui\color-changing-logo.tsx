"use client";

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";

interface ColorChangingLogoProps {
  className?: string;
  showText?: boolean;
}

export function ColorChangingLogo({ className, showText = true }: ColorChangingLogoProps) {
  const [colorIndex, setColorIndex] = useState(0);
  
  const colors = [
    "#FF5E5B", // Red
    "#6EB5FF", // Blue
    "#42E2B8", // Teal
    "#F7D002", // Yellow
    "#B28DFF", // Purple
    "#FF9966", // Orange
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setColorIndex((prevIndex) => (prevIndex + 1) % colors.length);
    }, 2000);
    
    return () => clearInterval(interval);
  }, []);

  return (
    
    <div className={cn("flex items-center", className)}>
      <svg
        width="36"
        height="36"
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={showText ? "mr-2" : ""}
      >
        <path
          d="M18 3C9.71573 3 3 9.71573 3 18C3 26.2843 9.71573 33 18 33C26.2843 33 33 26.2843 33 18C33 9.71573 26.2843 3 18 3Z"
          fill={colors[colorIndex]}
          className="transition-colors duration-700"
        />
        <path
          d="M18 7C11.9249 7 7 11.9249 7 18C7 24.0751 11.9249 29 18 29C24.0751 29 29 24.0751 29 18"
          stroke="white"
          strokeWidth="3"
          strokeLinecap="round"
        />
        <circle cx="18" cy="18" r="5" fill="white" />
      </svg>
      {showText && <span className="font-bold tracking-tight">Coloriqo</span>}
    </div>
  );
} 