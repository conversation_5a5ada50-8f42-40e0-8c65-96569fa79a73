


// Credit system constants
const CREDIT_MAX = 10;
const CREDIT_REGENERATION_MINUTES = 15; // Time to fully regenerate all credits
const STORAGE_KEY = "color-tool-credits"; // Use hardcoded value, not environment variable
const COOKIE_KEY = "color-tool-creds"; // Use hardcoded value, not environment variable
const SECRET_KEY = process.env.NEXT_PUBLIC_CREDIT_SECRET_KEY;
if (!SECRET_KEY) {
  throw new Error('NEXT_PUBLIC_CREDIT_SECRET_KEY environment variable is required');
}

interface CreditState {
  credits: number;
  lastUpdated: number; // Timestamp
  nextRegenerationTime: number; // Timestamp
}

// Check if running in browser environment
const isBrowser = typeof window !== 'undefined';

/**
 * Calculate a secure hash for credit data to prevent tampering
 */
function generateHash(data: string): string {
  try {
    if (!isBrowser) return "";
    
    // Use a more secure hash method
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data + SECRET_KEY);
    return btoa(String.fromCharCode(...new Uint8Array(dataBuffer)));
  } catch (error) {
    return "";
  }
}

/**
 * Verify the hash for credit data
 */
function verifyHash(data: string, hash: string): boolean {
  try {
    if (!isBrowser) return false;
    
    // Use the same secure algorithm for verification
    const expectedHash = generateHash(data);
    return hash === expectedHash;
  } catch (error) {
    return false;
  }
}

/**
 * Set a cookie with expiration and security flags
 */
function setCookie(name: string, value: string, days: number = 7): void {
  if (!isBrowser) return;
  
  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
  
  // Add security flags to cookies
  const cookieOptions = [
    `expires=${expires.toUTCString()}`,
    'path=/',
    'SameSite=Strict',
    'Secure', // Only send over HTTPS
    'HttpOnly' // Prevent JavaScript access
  ].join(';');
  
  document.cookie = `${name}=${value};${cookieOptions}`;
}

/**
 * Get a cookie by name
 */
function getCookie(name: string): string | null {
  if (!isBrowser) return null;
  
  const nameEQ = name + "=";
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
}

/**
 * Get current credit state with regeneration logic
 * @param forceRefresh If true, bypasses any caching and forces a complete recalculation
 */
export function getCreditState(forceRefresh: boolean = false): CreditState {
  if (!isBrowser) {
    return {
      credits: CREDIT_MAX,
      lastUpdated: Date.now(),
      nextRegenerationTime: 0
    };
  }

  try {
    // Default credit state
    let creditState: CreditState = {
      credits: CREDIT_MAX,
      lastUpdated: Date.now(),
      nextRegenerationTime: 0
    };
    
    // Try to get data from cookies first (for cross-session persistence)
    let storedData = getCookie(COOKIE_KEY);
    let storedHash = getCookie(`${COOKIE_KEY}-hash`);
    
    // If cookies don't exist or are invalid, fall back to localStorage
    if (!storedData || !storedHash || !verifyHash(storedData, storedHash)) {
      storedData = localStorage.getItem(STORAGE_KEY);
      storedHash = localStorage.getItem(`${STORAGE_KEY}-hash`);
    }
    
    // If we have valid stored data and it hasn't been tampered with
    if (storedData && storedHash && verifyHash(storedData, storedHash)) {
      const parsedData = JSON.parse(storedData) as CreditState;
      
      // Check if we need to fully regenerate credits
      const now = Date.now();
      
      // Check if the regeneration time has passed
      if (parsedData.nextRegenerationTime > 0 && parsedData.nextRegenerationTime <= now) {
        // If regeneration time has passed, reset to max credits
        creditState = {
          credits: CREDIT_MAX,
          lastUpdated: now,
          nextRegenerationTime: 0
        };
      } else {
        // If regeneration time hasn't passed yet, keep the current credits
        creditState = {
          credits: parsedData.credits,
          lastUpdated: now,
          nextRegenerationTime: parsedData.nextRegenerationTime
        };
      }
      
      // If user has less than max credits and no regeneration time is set,
      // set the next regeneration time
      if (creditState.credits < CREDIT_MAX && creditState.nextRegenerationTime === 0) {
        creditState.nextRegenerationTime = now + (CREDIT_REGENERATION_MINUTES * 60 * 1000);
      }
    }
    
    // Save the updated state
    saveCredits(creditState);
    
    return creditState;
  } catch (error) {
    // Return default state on error
    return {
      credits: CREDIT_MAX,
      lastUpdated: Date.now(),
      nextRegenerationTime: 0
    };
  }
}

/**
 * Use a credit and update storage
 */
export function useCredit(): CreditState {
  try {
    // Get the most up-to-date credit state
    const state = getCreditState(true);
    
    if (state.credits > 0) {
      const now = Date.now();
      
      // Deduct a credit
      const newState: CreditState = {
        credits: state.credits - 1,
        lastUpdated: now,
        nextRegenerationTime: state.credits === 1 ? 
          now + (CREDIT_REGENERATION_MINUTES * 60 * 1000) : // Set next regen time when out of credits
          state.nextRegenerationTime
      };
      
      // Ensure we save the updated state
      saveCredits(newState);
      
      // Do NOT read back the state immediately as it could trigger regeneration logic
      // Instead return the newState directly
      return newState;
    }
    
    return state;
  } catch (error) {
    throw new Error('Failed to use credit');
  }
}

/**
 * Save credit state with tamper protection
 */
function saveCredits(state: CreditState): void {
  if (!isBrowser) return;
  
  try {
    // Important: Clear existing values first to prevent conflicts
    localStorage.removeItem(STORAGE_KEY);
    localStorage.removeItem(`${STORAGE_KEY}-hash`);
    setCookie(COOKIE_KEY, "", -1); // Expire immediately
    setCookie(`${COOKIE_KEY}-hash`, "", -1); // Expire immediately
    
    // Now set the new values
    const stateStr = JSON.stringify(state);
    
    // Generate hash for tamper protection
    const hash = generateHash(stateStr);
    
    // Save to localStorage first
    try {
      localStorage.setItem(STORAGE_KEY, stateStr);
      localStorage.setItem(`${STORAGE_KEY}-hash`, hash);
    } catch (e) {
      // If localStorage fails, try cookies
      setCookie(COOKIE_KEY, stateStr, 30);
      setCookie(`${COOKIE_KEY}-hash`, hash, 30);
    }
    
    // Then save to cookies as backup
    try {
      setCookie(COOKIE_KEY, stateStr, 30);
      setCookie(`${COOKIE_KEY}-hash`, hash, 30);
    } catch (e) {
      // If both storage mechanisms fail, we'll just have to start fresh next time
    }
  } catch (error) {
    // Silent fail - we'll start fresh next time
  }
}

/**
 * Reset credits to max (for admin)
 */
export function resetCredits(): CreditState {
  const newState: CreditState = {
    credits: CREDIT_MAX,
    lastUpdated: Date.now(),
    nextRegenerationTime: 0
  };
  
  saveCredits(newState);
  return newState;
}

/**
 * Format time until next credit regeneration as a specific time
 */
export function formatTimeRemaining(timestamp: number): string {
  const now = Date.now();
  
  // Return immediately for completed timers
  if (timestamp <= now) return "now";
  
  // Convert to a Date object
  const refreshTime = new Date(timestamp);
  
  // Format the time in a user-friendly way
  const hours = refreshTime.getHours();
  const minutes = refreshTime.getMinutes();
  
  // Format as HH:MM AM/PM
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12 AM
  const formattedMinutes = minutes.toString().padStart(2, '0');
  
  return `${formattedHours}:${formattedMinutes} ${ampm}`;
}

/**
 * Get credit regeneration period in human-readable format
 */
export function getCreditRegenerationPeriod(): string {
  return `${CREDIT_REGENERATION_MINUTES} minutes`;
}

/**
 * Get the full date and time when credits will be available
 */
export function getFullRegenerationTime(timestamp: number): string {
  if (timestamp <= Date.now()) return "now";
  
  const refreshDate = new Date(timestamp);
  return refreshDate.toLocaleString('en-US', { 
    weekday: 'short',
    month: 'short', 
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  });
} 