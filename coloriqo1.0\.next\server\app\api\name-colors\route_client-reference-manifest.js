globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/name-colors/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/page-transition-wrapper.tsx":{"*":{"id":"(ssr)/./components/ui/page-transition-wrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/color-cards.tsx":{"*":{"id":"(ssr)/./components/color-cards.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"E:\\WeWiseLabs\\coloriqo1.0\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\components\\ui\\page-transition-wrapper.tsx":{"id":"(app-pages-browser)/./components/ui/page-transition-wrapper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Rubik\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-rubik\",\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"rubik\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Rubik\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-rubik\",\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"rubik\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\",\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"montserrat\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\",\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"montserrat\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Cinzel_Decorative\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-cinzel\",\"weight\":[\"400\",\"700\",\"900\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"cinzelDecorative\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Cinzel_Decorative\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-cinzel\",\"weight\":[\"400\",\"700\",\"900\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"cinzelDecorative\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat-body\",\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"montserratBody\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat-body\",\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"montserratBody\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\WeWiseLabs\\coloriqo1.0\\components\\color-cards.tsx":{"id":"(app-pages-browser)/./components/color-cards.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"E:\\WeWiseLabs\\coloriqo1.0\\":[],"E:\\WeWiseLabs\\coloriqo1.0\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"E:\\WeWiseLabs\\coloriqo1.0\\app\\page":[],"E:\\WeWiseLabs\\coloriqo1.0\\app\\api\\name-colors\\route":[]},"rscModuleMapping":{"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(rsc)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/page-transition-wrapper.tsx":{"*":{"id":"(rsc)/./components/ui/page-transition-wrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(rsc)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/color-cards.tsx":{"*":{"id":"(rsc)/./components/color-cards.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}