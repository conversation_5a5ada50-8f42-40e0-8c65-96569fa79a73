/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {

    remotePatterns: [
      
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
      },
      {
        protocol: 'https',
        hostname: 'player.cloudinary.com',
      }
    ],
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: `
              default-src 'self';
              script-src 'self' 'unsafe-eval' 'unsafe-inline' https://player.cloudinary.com;
              style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
              style-src-elem 'self' https://fonts.googleapis.com;
              font-src 'self' https://fonts.gstatic.com;
              img-src 'self' data: blob: https:;
              media-src 'self' https://res.cloudinary.com blob:;
              connect-src 'self' https: https://*.cloudinary.com;
              frame-src 'self' https: https://player.cloudinary.com;
              worker-src 'self' blob:;
            `.replace(/\s{2,}/g, ' ').trim(),
          },
        ],
      },
    ];
  },
};

export default nextConfig;