"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Upload, Plus, Pipette } from "lucide-react"

export default function ColorCards() {
  // Default colors
  const defaultColors = [
    { name: "Primary", hex: "#4C6EF5", tailwind: "custom-primary" },
    { name: "Secondary", hex: "#404040", tailwind: "custom-secondary" },
    { name: "Accent", hex: "#F8F9FA", tailwind: "custom-accent" },
  ]

  const [colors, setColors] = useState(defaultColors)
  const [extractedColors, setExtractedColors] = useState<{ name: string; hex: string }[]>([])
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [isPickingColor, setIsPickingColor] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader()
      reader.onload = (event) => {
        setSelectedImage(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Draw image on canvas when selected
  useEffect(() => {
    if (selectedImage && canvasRef.current) {
      const canvas = canvasRef.current
      const ctx = canvas.getContext("2d")

      if (ctx) {
        const img = new Image()
        img.crossOrigin = "anonymous"
        img.onload = () => {
          // Calculate aspect ratio to fit within canvas
          const maxWidth = 600
          const maxHeight = 400
          let width = img.width
          let height = img.height

          if (width > maxWidth) {
            height = (maxWidth / width) * height
            width = maxWidth
          }

          if (height > maxHeight) {
            width = (maxHeight / height) * width
            height = maxHeight
          }

          // Set canvas dimensions
          canvas.width = width
          canvas.height = height

          // Draw image on canvas
          ctx.drawImage(img, 0, 0, width, height)
        }
        img.src = selectedImage
      }
    }
  }, [selectedImage])

  // Extract color from canvas at clicked position
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isPickingColor || !canvasRef.current) return

    const canvas = canvasRef.current
    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    const ctx = canvas.getContext("2d")
    if (ctx) {
      const pixelData = ctx.getImageData(x, y, 1, 1).data
      const hex = rgbToHex(pixelData[0], pixelData[1], pixelData[2])

      // Add extracted color to list
      const colorName = `Extracted ${extractedColors.length + 1}`
      setExtractedColors([...extractedColors, { name: colorName, hex }])

      // Exit color picking mode
      setIsPickingColor(false)
    }
  }

  // Convert RGB to HEX
  const rgbToHex = (r: number, g: number, b: number) => {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase()
  }

  // Trigger file input click
  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  // Toggle color picking mode
  const toggleColorPicker = () => {
    setIsPickingColor(!isPickingColor)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6 text-center">Color Palette</h1>

      {/* Default color cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {colors.map((color) => (
          <Card key={color.name} className="overflow-hidden">
            <div
              className={`h-32 bg-${color.tailwind}`}
              style={{ backgroundColor: color.hex }}
              aria-label={`${color.name} color sample`}
            ></div>
            <CardHeader>
              <CardTitle>{color.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">Hex: {color.hex}</p>
              <p className="text-sm text-gray-600">Tailwind: bg-{color.tailwind}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Image upload section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Extract Colors from Image</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="flex gap-4">
              <Button onClick={handleUploadClick} variant="outline">
                <Upload className="mr-2 h-4 w-4" /> Upload Image
              </Button>
              <input type="file" ref={fileInputRef} onChange={handleFileChange} accept="image/*" className="hidden" />
              {selectedImage && (
                <Button
                  onClick={toggleColorPicker}
                  variant={isPickingColor ? "default" : "outline"}
                  className={isPickingColor ? "bg-custom-primary" : ""}
                >
                  <Pipette className="mr-2 h-4 w-4" />
                  {isPickingColor ? "Picking Color..." : "Pick Color"}
                </Button>
              )}
            </div>

            {selectedImage && (
              <div className="mt-4 relative">
                <canvas
                  ref={canvasRef}
                  onClick={handleCanvasClick}
                  className={`border border-gray-200 rounded-md max-w-full ${isPickingColor ? "cursor-crosshair" : ""}`}
                />
                {isPickingColor && (
                  <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white px-3 py-1 rounded-md text-sm">
                    Click anywhere on the image to extract a color
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Extracted colors section */}
      {extractedColors.length > 0 && (
        <>
          <h2 className="text-xl font-bold mb-4">Extracted Colors</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {extractedColors.map((color, index) => (
              <Card key={index} className="overflow-hidden">
                <div
                  className="h-24"
                  style={{ backgroundColor: color.hex }}
                  aria-label={`${color.name} color sample`}
                ></div>
                <CardContent className="p-4">
                  <p className="font-medium">{color.name}</p>
                  <p className="text-sm text-gray-600">{color.hex}</p>
                </CardContent>
              </Card>
            ))}
            <Card
              className="overflow-hidden border-dashed border-2 flex items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={toggleColorPicker}
            >
              <div className="p-6 flex flex-col items-center">
                <Plus className="h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">Extract another color</p>
              </div>
            </Card>
          </div>
        </>
      )}
    </div>
  )
}
