"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tool/page",{

/***/ "(app-pages-browser)/./components/color-cards.tsx":
/*!************************************!*\
  !*** ./components/color-cards.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ColorCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pipette.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-panel-top.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hand.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _hooks_use_color_extractor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-color-extractor */ \"(app-pages-browser)/./hooks/use-color-extractor.ts\");\n/* harmony import */ var _components_ui_loader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/loader */ \"(app-pages-browser)/./components/ui/loader.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _ui_code_preview__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/code-preview */ \"(app-pages-browser)/./components/ui/code-preview.tsx\");\n/* harmony import */ var _ui_magnifier__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/magnifier */ \"(app-pages-browser)/./components/ui/magnifier.tsx\");\n/* harmony import */ var _ui_theme_switcher__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ui/theme-switcher */ \"(app-pages-browser)/./components/ui/theme-switcher.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _lib_credit_service__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/credit-service */ \"(app-pages-browser)/./lib/credit-service.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ColorCards() {\n    _s();\n    // Constants\n    const COLOR_LIMIT_STANDARD = 10;\n    const ADMIN_CODE = \"creatordev\";\n    if (!ADMIN_CODE) {\n        throw new Error('NEXT_PUBLIC_ADMIN_CODE environment variable is required');\n    }\n    const COLORS_PER_CREDIT = 5 // Number of colors per credit\n    ;\n    // Constants for session storage\n    const SESSION_COLORS_KEY = \"color-tool-colors\";\n    const SESSION_IMAGE_KEY = \"color-tool-image\";\n    // State to track if session storage should be cleared\n    const [sessionCleared, setSessionCleared] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Memoize session storage operations with sessionCleared dependency\n    const storedColors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ColorCards.useMemo[storedColors]\": ()=>{\n            if ( true && !sessionCleared) {\n                try {\n                    const storedColors = sessionStorage.getItem(SESSION_COLORS_KEY);\n                    if (storedColors) {\n                        return JSON.parse(storedColors);\n                    }\n                } catch (error) {\n                // Silent fail - start with empty colors\n                }\n            }\n            return [];\n        }\n    }[\"ColorCards.useMemo[storedColors]\"], [\n        sessionCleared\n    ]);\n    // Memoize image retrieval with sessionCleared dependency\n    const storedImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ColorCards.useMemo[storedImage]\": ()=>{\n            if ( true && !sessionCleared) {\n                try {\n                    return sessionStorage.getItem(SESSION_IMAGE_KEY);\n                } catch (error) {\n                // Silent fail - start with no image\n                }\n            }\n            return null;\n        }\n    }[\"ColorCards.useMemo[storedImage]\"], [\n        sessionCleared\n    ]);\n    // States\n    const [extractedColors, setExtractedColors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedColors);\n    const [copiedColor, setCopiedColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [extractionMethod, setExtractionMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ai\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"upload\");\n    const [showCodePreview, setShowCodePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMagnifier, setShowMagnifier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPixelColor, setCurrentPixelColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAdminMode, setIsAdminMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [adminCodeInput, setAdminCodeInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showAdminInput, setShowAdminInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [creditState, setCreditState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ColorCards.useState\": ()=>(0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)()\n    }[\"ColorCards.useState\"]);\n    const [timeRemaining, setTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [manualPicksCount, setManualPicksCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // Count manual color picks\n    ;\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // State for mobile menu toggle\n    ;\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Create ref outside of useEffect to track credit state\n    const creditStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(creditState);\n    // Update ref whenever creditState changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            creditStateRef.current = creditState;\n        }\n    }[\"ColorCards.useEffect\"], [\n        creditState\n    ]);\n    // Update credit info and handle timer countdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            // Initial state fetch\n            const initialState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)(true);\n            setCreditState(initialState);\n            // Display and update timer\n            const timerInterval = setInterval({\n                \"ColorCards.useEffect.timerInterval\": ()=>{\n                    // Get current credit state\n                    const currentState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)(false);\n                    // If there's a regeneration time, show countdown\n                    if (currentState.nextRegenerationTime > 0) {\n                        // Calculate and display time remaining\n                        const timeLeft = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.formatTimeRemaining)(currentState.nextRegenerationTime);\n                        setTimeRemaining(timeLeft);\n                        // Check if time is up (credits should regenerate)\n                        if (timeLeft === \"now\") {\n                            // Force refresh to get updated credits\n                            const refreshedState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)(true);\n                            // Only update state if credits actually changed\n                            if (refreshedState.credits !== creditStateRef.current.credits) {\n                                setCreditState(refreshedState);\n                                // Show notification only if credits increased\n                                if (refreshedState.credits > creditStateRef.current.credits) {\n                                    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                                        title: \"Credits refreshed!\",\n                                        description: \"You have new credits available.\",\n                                        duration: 3000\n                                    });\n                                }\n                            }\n                        }\n                    } else {\n                        // No regeneration time, clear the timer display\n                        setTimeRemaining(\"\");\n                    }\n                    // Only update credit state if it has actually changed\n                    if (currentState.credits !== creditStateRef.current.credits || currentState.nextRegenerationTime !== creditStateRef.current.nextRegenerationTime) {\n                        setCreditState(currentState);\n                    }\n                }\n            }[\"ColorCards.useEffect.timerInterval\"], 1000); // Update every second\n            return ({\n                \"ColorCards.useEffect\": ()=>{\n                    clearInterval(timerInterval);\n                }\n            })[\"ColorCards.useEffect\"];\n        }\n    }[\"ColorCards.useEffect\"], []); // No dependencies to avoid re-creating the interval\n    // Update credit state - memoize this function to improve performance\n    const updateCreditState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ColorCards.useCallback[updateCreditState]\": function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            const newState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)(forceRefresh);\n            setCreditState(newState);\n            return newState;\n        }\n    }[\"ColorCards.useCallback[updateCreditState]\"], []);\n    // Show current credit status\n    const showCreditStatus = ()=>{\n        const state = updateCreditState();\n        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n            title: \"Credit Status\",\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Available credits: \",\n                            state.credits\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    state.nextRegenerationTime > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Credits will reset at: \",\n                            (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.formatTimeRemaining)(state.nextRegenerationTime)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 13\n                    }, this),\n                    state.nextRegenerationTime > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground mt-1\",\n                        children: (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getFullRegenerationTime)(state.nextRegenerationTime)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this),\n            duration: 7000\n        });\n    };\n    // Use a credit for extraction\n    const consumeCredit = ()=>{\n        if (isAdminMode) {\n            return true; // Admin has unlimited credits\n        }\n        // Get fresh state directly from service\n        const currentState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)(true);\n        if (currentState.credits > 0) {\n            try {\n                // Use credit and get new state\n                const newState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.useCredit)();\n                // Update component state\n                setCreditState(newState);\n                // Show toast to confirm credit usage\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Credit used\",\n                    description: \"1 credit consumed. \".concat(newState.credits, \" credits remaining.\"),\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Error\",\n                    description: \"Could not process credit. Please try again.\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n        return false;\n    };\n    // Update credits based on admin status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            if (isAdminMode) {\n                // Reset credits if admin mode is activated\n                (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.resetCredits)();\n                updateCreditState();\n            }\n        }\n    }[\"ColorCards.useEffect\"], [\n        isAdminMode\n    ]);\n    // Listen for custom event to open admin input\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            const handleOpenAdminInput = {\n                \"ColorCards.useEffect.handleOpenAdminInput\": ()=>{\n                    setShowAdminInput(true);\n                }\n            }[\"ColorCards.useEffect.handleOpenAdminInput\"];\n            window.addEventListener('openAdminInput', handleOpenAdminInput);\n            return ({\n                \"ColorCards.useEffect\": ()=>{\n                    window.removeEventListener('openAdminInput', handleOpenAdminInput);\n                }\n            })[\"ColorCards.useEffect\"];\n        }\n    }[\"ColorCards.useEffect\"], []);\n    // Clean up any resources on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            return ({\n                \"ColorCards.useEffect\": ()=>{\n                    // Clean up any ObjectURLs or other resources\n                    if (selectedFile) {\n                        setSelectedFile(null);\n                    }\n                }\n            })[\"ColorCards.useEffect\"];\n        }\n    }[\"ColorCards.useEffect\"], []);\n    // Save colors to session storage when they change - debounced to reduce writes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            // Use a timeout to debounce multiple quick updates\n            const saveTimeout = setTimeout({\n                \"ColorCards.useEffect.saveTimeout\": ()=>{\n                    if ( true && extractedColors.length > 0) {\n                        try {\n                            sessionStorage.setItem(SESSION_COLORS_KEY, JSON.stringify(extractedColors));\n                        } catch (error) {\n                        // Silent fail - colors will be lost on refresh\n                        }\n                    }\n                }\n            }[\"ColorCards.useEffect.saveTimeout\"], 300); // Debounce for 300ms\n            // Clear timeout on cleanup\n            return ({\n                \"ColorCards.useEffect\": ()=>clearTimeout(saveTimeout)\n            })[\"ColorCards.useEffect\"];\n        }\n    }[\"ColorCards.useEffect\"], [\n        extractedColors\n    ]);\n    // Use our custom hook for color extraction\n    const { isExtracting, selectedImage, setSelectedImage, isPickingColor, extractionError, imageSize, toggleColorPicker, handleFileSelect, extractColors } = (0,_hooks_use_color_extractor__WEBPACK_IMPORTED_MODULE_7__.useColorExtractor)({\n        onExtractedColors: {\n            \"ColorCards.useColorExtractor\": (colors)=>{\n                setExtractedColors({\n                    \"ColorCards.useColorExtractor\": (prev)=>{\n                        const newColors = [\n                            ...prev,\n                            ...colors\n                        ];\n                        // Save to session storage if not cleared\n                        if (!sessionCleared && \"object\" !== 'undefined') {\n                            sessionStorage.setItem(SESSION_COLORS_KEY, JSON.stringify(newColors));\n                        }\n                        return newColors;\n                    }\n                }[\"ColorCards.useColorExtractor\"]);\n            }\n        }[\"ColorCards.useColorExtractor\"],\n        canvasRef,\n        existingColors: extractedColors,\n        isAdminMode,\n        initialImage: storedImage\n    });\n    // Save image to session storage when it changes - debounced\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            // Debounce to prevent excessive writes\n            const saveTimeout = setTimeout({\n                \"ColorCards.useEffect.saveTimeout\": ()=>{\n                    if ( true && selectedImage && !sessionCleared) {\n                        try {\n                            sessionStorage.setItem(SESSION_IMAGE_KEY, selectedImage);\n                        } catch (error) {\n                        // Silent fail - image will be lost on refresh\n                        }\n                    }\n                }\n            }[\"ColorCards.useEffect.saveTimeout\"], 300); // Debounce for 300ms\n            return ({\n                \"ColorCards.useEffect\": ()=>clearTimeout(saveTimeout)\n            })[\"ColorCards.useEffect\"];\n        }\n    }[\"ColorCards.useEffect\"], [\n        selectedImage,\n        sessionCleared\n    ]);\n    // Handle mouse movement over the canvas\n    const handleMouseMove = (e)=>{\n        if (!canvasRef.current || extractionMethod !== \"manual\") return;\n        const canvas = canvasRef.current;\n        const rect = canvas.getBoundingClientRect();\n        // Calculate the cursor position relative to the canvas\n        const x = Math.min(Math.max(0, e.clientX - rect.left), canvas.width - 1);\n        const y = Math.min(Math.max(0, e.clientY - rect.top), canvas.height - 1);\n        setMousePosition({\n            x,\n            y\n        });\n        // Get the pixel color under the cursor\n        try {\n            const ctx = canvas.getContext(\"2d\");\n            if (ctx) {\n                const pixelData = ctx.getImageData(x, y, 1, 1).data;\n                const pixelColor = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.rgbToHex)(pixelData[0], pixelData[1], pixelData[2]);\n                setCurrentPixelColor(pixelColor);\n            }\n        } catch (error) {\n        // Silent fail - color picker will not show current color\n        }\n    };\n    // Calculate magnifier position to keep it within viewport bounds\n    const getMagnifierPosition = (x, y, canvasRect, size)=>{\n        const viewportWidth = window.innerWidth;\n        const viewportHeight = window.innerHeight;\n        // Default position (to the right of cursor)\n        let posX = x + 20;\n        let posY = y - 75;\n        // Adjust if too close to right edge\n        if (posX + size > viewportWidth - 20) {\n            posX = x - size - 20 // Position to the left of cursor\n            ;\n        }\n        // Adjust if too close to bottom edge\n        if (posY + size > viewportHeight - 20) {\n            posY = viewportHeight - size - 20;\n        }\n        // Adjust if too close to top edge\n        if (posY < 20) {\n            posY = 20;\n        }\n        return {\n            left: posX,\n            top: posY\n        };\n    };\n    // Handle mouse enter on canvas\n    const handleMouseEnter = ()=>{\n        if (extractionMethod === \"manual\" && isPickingColor && (isAdminMode || creditState.credits > 0)) {\n            setShowMagnifier(true);\n        }\n    };\n    // Handle mouse leave on canvas\n    const handleMouseLeave = ()=>{\n        setShowMagnifier(false);\n        setMousePosition(null);\n        setCurrentPixelColor(\"\");\n    };\n    // Get all colors for display and export - memoized to prevent recalculations\n    const allColors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ColorCards.useMemo[allColors]\": ()=>extractedColors\n    }[\"ColorCards.useMemo[allColors]\"], [\n        extractedColors\n    ]);\n    // Trigger file input click\n    const handleUploadClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    // Handle file change with additional security checks\n    const handleFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            // Check file size (limit to 5MB)\n            const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\n            if (file.size > MAX_FILE_SIZE) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"File too large\",\n                    description: \"The image must be smaller than 5MB\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Check file type - expanded list of supported formats\n            const allowedTypes = [\n                'image/jpeg',\n                'image/png',\n                'image/webp',\n                'image/gif',\n                'image/bmp',\n                'image/tiff',\n                'image/svg+xml',\n                'image/x-icon',\n                'image/vnd.microsoft.icon',\n                'image/heic',\n                'image/heif',\n                'image/avif',\n                'image/jp2',\n                'image/jpx',\n                'image/jpm',\n                'image/jxl'\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Invalid file type\",\n                    description: \"Please upload a supported image format (JPEG, PNG, WebP, GIF, BMP, TIFF, SVG, ICO, HEIC, AVIF, JPEG2000, JPEG XL)\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Validate image dimensions\n            const img = new Image();\n            img.onload = ()=>{\n                const MAX_DIMENSION = 4096; // Maximum width or height\n                if (img.width > MAX_DIMENSION || img.height > MAX_DIMENSION) {\n                    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                        title: \"Image too large\",\n                        description: \"Image dimensions must be less than 4096x4096 pixels\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const selectedFile = handleFileSelect(file);\n                if (selectedFile) {\n                    setSelectedFile(selectedFile);\n                }\n            };\n            img.onerror = ()=>{\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Invalid image\",\n                    description: \"The file appears to be corrupted or invalid\",\n                    variant: \"destructive\"\n                });\n            };\n            img.src = URL.createObjectURL(file);\n        }\n    };\n    // Name colors using AI\n    const nameColorsWithAI = async (hexColors)=>{\n        try {\n            const response = await fetch(\"/api/name-colors\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    colors: hexColors\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to name colors\");\n            }\n            const data = await response.json();\n            return data.colors;\n        } catch (error) {\n            // Fallback to local naming if AI fails\n            return hexColors.map((hex)=>({\n                    hex,\n                    name: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.getColorName)(hex)\n                }));\n        }\n    };\n    // Handle starting color extraction with the selected method\n    const handleExtract = async ()=>{\n        if (selectedFile) {\n            // Force refresh credit state before checking\n            const freshState = updateCreditState(true);\n            // Check if we have credits available or are in admin mode\n            if (isAdminMode || freshState.credits > 0) {\n                // For AI extraction, we'll consume 1 credit for up to 5 colors\n                if (extractionMethod === \"ai\" && !isAdminMode) {\n                    // Always consume a credit BEFORE extraction for AI method\n                    const creditConsumed = consumeCredit();\n                    if (!creditConsumed) {\n                        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                            title: \"Error\",\n                            description: \"Could not process credit. Please try again.\",\n                            variant: \"destructive\",\n                            duration: 3000\n                        });\n                        return;\n                    }\n                    // Show a toast notification about AI extracting colors\n                    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                        title: \"AI is analyzing your image\",\n                        description: \"Extracting the most prominent and visually important colors...\",\n                        duration: 5000\n                    });\n                    await extractColors(selectedFile, extractionMethod);\n                } else {\n                    await extractColors(selectedFile, extractionMethod);\n                }\n            } else {\n                // If no credits, show toast with info about regeneration\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"No credits available\",\n                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Credits will be available at \",\n                                    timeRemaining,\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs mt-1 cursor-pointer text-blue-500\",\n                                onClick: toggleAdminInput,\n                                children: \"Upgrade for unlimited access\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 13\n                    }, this),\n                    duration: 5000\n                });\n            }\n        } else {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"No image selected\",\n                description: \"Please upload an image first.\",\n                variant: \"destructive\",\n                duration: 3000\n            });\n        }\n    };\n    // Extract a single color from the canvas when clicked\n    const handleCanvasClick = async (e)=>{\n        if (!canvasRef.current) return;\n        // Check if we're in manual mode, if not, don't do anything\n        if (extractionMethod !== \"manual\") return;\n        // Check if we have credits available or are in admin mode\n        if (!isAdminMode && creditState.credits === 0) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"No credits available\",\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Credits will be available at \",\n                                timeRemaining,\n                                \".\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs mt-1 cursor-pointer text-blue-500\",\n                            onClick: toggleAdminInput,\n                            children: \"Upgrade for unlimited access\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, this),\n                duration: 5000\n            });\n            return;\n        }\n        try {\n            const canvas = canvasRef.current;\n            const rect = canvas.getBoundingClientRect();\n            const x = Math.min(Math.max(0, e.clientX - rect.left), canvas.width - 1);\n            const y = Math.min(Math.max(0, e.clientY - rect.top), canvas.height - 1);\n            const ctx = canvas.getContext(\"2d\");\n            if (ctx) {\n                const pixelData = ctx.getImageData(x, y, 1, 1).data;\n                const hex = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.rgbToHex)(pixelData[0], pixelData[1], pixelData[2]);\n                // Check if this color already exists\n                if (extractedColors.some((color)=>color.hex.toLowerCase() === hex.toLowerCase())) {\n                    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                        title: \"Duplicate color\",\n                        description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-4 rounded-full border\",\n                                    style: {\n                                        backgroundColor: hex\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"This color is already in your palette\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 15\n                        }, this),\n                        duration: 3000\n                    });\n                    return;\n                }\n                // For manual picking, only consume a credit every COLORS_PER_CREDIT colors\n                if (!isAdminMode) {\n                    const newPicksCount = manualPicksCount + 1;\n                    setManualPicksCount(newPicksCount);\n                    // If we've reached the threshold, consume a credit and reset the counter\n                    if (newPicksCount >= COLORS_PER_CREDIT) {\n                        const creditConsumed = consumeCredit();\n                        if (!creditConsumed) {\n                            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                                title: \"Credit deduction failed\",\n                                description: \"Could not deduct credit. Please try again.\",\n                                variant: \"destructive\",\n                                duration: 3000\n                            });\n                            return;\n                        }\n                        setManualPicksCount(0);\n                    // Credit usage toast is now handled in consumeCredit function\n                    } else {\n                        // Show remaining picks\n                        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                            title: \"Color extracted!\",\n                            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 w-4 rounded-full border\",\n                                                style: {\n                                                    backgroundColor: hex\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: hex\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            COLORS_PER_CREDIT - newPicksCount,\n                                            \" more picks until 1 credit is used\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 17\n                            }, this),\n                            duration: 2000\n                        });\n                    }\n                }\n                // Set a loading state\n                const tempColorId = Date.now().toString();\n                const tempColor = {\n                    name: \"Naming...\",\n                    hex,\n                    id: tempColorId\n                };\n                setExtractedColors((prev)=>[\n                        ...prev,\n                        tempColor\n                    ]);\n                // Get color name from AI\n                try {\n                    const namedColors = await nameColorsWithAI([\n                        hex\n                    ]);\n                    if (namedColors && namedColors.length > 0) {\n                        // Update the color with the AI-generated name\n                        setExtractedColors((prev)=>prev.map((color)=>color.id === tempColorId ? {\n                                    ...namedColors[0],\n                                    id: tempColorId\n                                } : color));\n                        // Only show toast if we're in admin mode (otherwise it's shown in the credit handling code)\n                        if (isAdminMode) {\n                            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                                title: \"Color extracted!\",\n                                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 w-4 rounded-full border\",\n                                            style: {\n                                                backgroundColor: hex\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                namedColors[0].name,\n                                                \" (\",\n                                                hex,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 19\n                                }, this),\n                                duration: 3000\n                            });\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"Error getting color name:\", error);\n                    // If AI naming fails, use the local getColorName function as fallback\n                    const colorName = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.getColorName)(hex);\n                    setExtractedColors((prev)=>prev.map((color)=>color.id === tempColorId ? {\n                                name: colorName,\n                                hex,\n                                id: tempColorId\n                            } : color));\n                    // Only show toast if we're in admin mode (otherwise it's shown in the credit handling code)\n                    if (isAdminMode) {\n                        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                            title: \"Color extracted!\",\n                            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-4 rounded-full border\",\n                                        style: {\n                                            backgroundColor: hex\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            colorName,\n                                            \" (\",\n                                            hex,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 17\n                            }, this),\n                            duration: 3000\n                        });\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Error extracting color:\", error);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Error extracting color\",\n                description: \"Could not read pixel data from the image\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Copy color to clipboard with error handling\n    const copyColor = (hex)=>{\n        try {\n            navigator.clipboard.writeText(hex).then(()=>{\n                setCopiedColor(hex);\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Color copied!\",\n                    description: \"\".concat(hex, \" has been copied to clipboard.\"),\n                    duration: 2000\n                });\n                // Reset copied state after 2 seconds\n                setTimeout(()=>{\n                    setCopiedColor(null);\n                }, 2000);\n            }).catch(()=>{\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Failed to copy\",\n                    description: \"Could not copy to clipboard. Try again or copy manually.\",\n                    variant: \"destructive\"\n                });\n            });\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Failed to copy\",\n                description: \"Could not copy to clipboard. Try again or copy manually.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Show code preview \n    const handleShowCodePreview = ()=>{\n        if (allColors.length === 0) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"No colors to export\",\n                description: \"Extract some colors first before viewing code.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Color codes should always be available to view (not premium)\n        setShowCodePreview(true);\n    };\n    // Export palette as a file\n    const exportPalette = ()=>{\n        if (allColors.length === 0) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"No colors to export\",\n                description: \"Extract some colors first before exporting.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const jsonContent = JSON.stringify(allColors, null, 2);\n            const blob = new Blob([\n                jsonContent\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"color-palette.json\";\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Palette exported!\",\n                description: \"Color palette has been exported as JSON file.\",\n                duration: 3000\n            });\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Export failed\",\n                description: \"Could not export the color palette. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Remove a color from extracted colors\n    const removeColor = (index)=>{\n        setExtractedColors((prev)=>prev.filter((_, i)=>i !== index));\n        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n            title: \"Color removed\",\n            description: \"The color has been removed from your palette.\",\n            duration: 2000\n        });\n    };\n    // Clear all extracted colors\n    const clearAllColors = ()=>{\n        if (extractedColors.length === 0) return;\n        setExtractedColors([]);\n        // Clear colors from session storage\n        if (true) {\n            sessionStorage.removeItem(SESSION_COLORS_KEY);\n        }\n        setSessionCleared(true);\n        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n            title: \"Colors cleared\",\n            description: \"All extracted colors have been cleared.\",\n            duration: 2000\n        });\n    };\n    // Remove the image\n    const removeImage = ()=>{\n        setSelectedFile(null);\n        setSelectedImage(null);\n        // Clear image from session storage\n        if (true) {\n            sessionStorage.removeItem(SESSION_IMAGE_KEY);\n        }\n        setSessionCleared(true);\n        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n            title: \"Image removed\",\n            description: \"The image has been removed.\",\n            duration: 2000\n        });\n    };\n    // Toggle the color picker with magnifier setup\n    const handleToggleColorPicker = ()=>{\n        const newState = !isPickingColor;\n        toggleColorPicker();\n        // If we're turning off the color picker, hide magnifier\n        if (!newState) {\n            setShowMagnifier(false);\n        }\n    };\n    // Handle extraction method change\n    const handleExtractionMethodChange = (value)=>{\n        const method = value;\n        setExtractionMethod(method);\n        if (method === \"manual\") {\n            // Always enable color picking mode when switching to manual\n            if (!isPickingColor) {\n                toggleColorPicker();\n            }\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Manual mode activated\",\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Click anywhere on the image to pick colors\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 862,\n                    columnNumber: 11\n                }, this),\n                duration: 3000\n            });\n        } else {\n            // When switching to AI mode, hide magnifier and disable color picking if active\n            setShowMagnifier(false);\n            if (isPickingColor) {\n                toggleColorPicker();\n            }\n        }\n    };\n    // Verify admin code\n    const verifyAdminCode = (code)=>{\n        if (code === ADMIN_CODE) {\n            setIsAdminMode(true);\n            setShowAdminInput(false);\n            // Reset credits for admin (unlimited)\n            (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.resetCredits)();\n            updateCreditState();\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Admin mode activated\",\n                description: \"No color extraction limits applied\",\n                duration: 3000\n            });\n        } else {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Invalid code\",\n                description: \"The code entered is not valid\",\n                variant: \"destructive\",\n                duration: 3000\n            });\n        }\n        setAdminCodeInput(\"\");\n    };\n    // Toggle admin input visibility\n    const toggleAdminInput = ()=>{\n        setShowAdminInput((prev)=>!prev);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 flex flex-col bg-background overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"border-b bg-card p-4 flex items-center justify-between shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 ml-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-6 w-6 text-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md\",\n                                                    style: {\n                                                        maskImage: 'url(\"data:image/svg+xml,%3Csvg xmlns=\\'http://www.w3.org/2000/svg\\' width=\\'24\\' height=\\'24\\' viewBox=\\'0 0 24 24\\' fill=\\'none\\' stroke=\\'currentColor\\' stroke-width=\\'2\\' stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\'%3E%3Cpath d=\\'M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121\\'/%3E%3C/svg%3E\")',\n                                                        maskSize: 'cover'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 917,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400\",\n                                            style: {\n                                                fontFamily: \"var(--font-montserrat)\",\n                                                letterSpacing: \"-0.5px\",\n                                                fontWeight: \"800\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_15___default()), {\n                                                href: \"/\",\n                                                \"data-barba\": \"wrapper\",\n                                                children: \"Coloriqo\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 920,\n                                                columnNumber: 16\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 919,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 914,\n                                    columnNumber: 13\n                                }, this),\n                                isAdminMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full font-medium\",\n                                    children: \"Admin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-3 py-1 border rounded-md bg-background\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: [\n                                                isAdminMode ? \"∞\" : creditState.credits,\n                                                \" Credits\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 15\n                                        }, this),\n                                        creditState.credits === 0 && !isAdminMode && timeRemaining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-xs text-muted-foreground ml-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Available at \",\n                                                        timeRemaining\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 940,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 938,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isAdminMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            className: \"h-6 ml-1 px-2 text-xs text-blue-500\",\n                                            onClick: showCreditStatus,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 950,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Status\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_theme_switcher__WEBPACK_IMPORTED_MODULE_12__.ThemeSwitcher, {}, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdminMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-200\",\n                                            onClick: ()=>{\n                                                setIsAdminMode(false);\n                                                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                                                    title: \"Logged out\",\n                                                    description: \"Returned to standard user mode\",\n                                                    duration: 3000\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 971,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Admin Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: toggleAdminInput,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 979,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Admin Login\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 974,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleShowCodePreview,\n                                            disabled: extractedColors.length === 0 ? true : false,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 989,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" View Code\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 983,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: exportPalette,\n                                            disabled: extractedColors.length === 0 ? true : false,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 997,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" Export\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 991,\n                                            columnNumber: 15\n                                        }, this),\n                                        extractedColors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: clearAllColors,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Clear All\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 17\n                                        }, this) : null\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 930,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex md:hidden items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1 px-2 py-1 border rounded-md bg-background text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-3 w-3 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: isAdminMode ? \"∞\" : creditState.credits\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1012,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1010,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_theme_switcher__WEBPACK_IMPORTED_MODULE_12__.ThemeSwitcher, {}, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1018,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    className: \"h-9 w-9 relative overflow-hidden group hover:bg-accent transition-colors\",\n                                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                    \"aria-label\": \"Toggle navigation menu\",\n                                    \"aria-expanded\": isMobileMenuOpen,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col justify-center items-center w-5 h-5 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform \".concat(isMobileMenuOpen ? 'rotate-45 translate-y-0' : '-translate-y-1.5')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out \".concat(isMobileMenuOpen ? 'opacity-0 scale-0' : 'opacity-100 scale-100')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform \".concat(isMobileMenuOpen ? '-rotate-45 translate-y-0' : 'translate-y-1.5')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 1008,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden fixed inset-0 z-50 transition-all duration-300 \".concat(isMobileMenuOpen ? 'visible' : 'invisible'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-black/50 transition-opacity duration-300 \".concat(isMobileMenuOpen ? 'opacity-100' : 'opacity-0'),\n                                    onClick: ()=>setIsMobileMenuOpen(false)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1057,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 top-0 h-full w-80 bg-background border-l shadow-lg transform transition-transform duration-300 \".concat(isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: \"Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1066,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"h-8 w-8\",\n                                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                                    \"aria-label\": \"Close menu\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"block h-0.5 w-4 bg-current rotate-45\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1074,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"block h-0.5 w-4 bg-current -rotate-45 -translate-y-0.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1075,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1065,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col p-4 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Admin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isAdminMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-200\",\n                                                            onClick: ()=>{\n                                                                setIsAdminMode(false);\n                                                                setIsMobileMenuOpen(false);\n                                                                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                                                                    title: \"Logged out\",\n                                                                    description: \"Returned to standard user mode\",\n                                                                    duration: 3000\n                                                                });\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1099,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Admin Logout\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1085,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            onClick: ()=>{\n                                                                toggleAdminInput();\n                                                                setIsMobileMenuOpen(false);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1111,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Admin Login\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1102,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1082,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Tools\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1118,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            onClick: ()=>{\n                                                                handleShowCodePreview();\n                                                                setIsMobileMenuOpen(false);\n                                                            },\n                                                            disabled: extractedColors.length === 0 ? true : false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1129,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" View Code\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1119,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            onClick: ()=>{\n                                                                exportPalette();\n                                                                setIsMobileMenuOpen(false);\n                                                            },\n                                                            disabled: extractedColors.length === 0 ? true : false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1141,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" Export\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1131,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        extractedColors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            onClick: ()=>{\n                                                                clearAllColors();\n                                                                setIsMobileMenuOpen(false);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1153,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Clear All\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1144,\n                                                            columnNumber: 21\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Credits\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1160,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 px-3 py-2 border rounded-md bg-background\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1162,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: [\n                                                                        isAdminMode ? \"∞\" : creditState.credits,\n                                                                        \" Credits\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1163,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1161,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        creditState.credits === 0 && !isAdminMode && timeRemaining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-xs text-muted-foreground px-3 py-2 border rounded-md bg-muted/50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-3 w-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1169,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Available at \",\n                                                                        timeRemaining\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1170,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1168,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        !isAdminMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full text-blue-500\",\n                                                            onClick: ()=>{\n                                                                showCreditStatus();\n                                                                setIsMobileMenuOpen(false);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1183,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Check Status\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1174,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1080,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 1055,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 911,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                            className: \"w-64 border-r bg-muted/40 p-4 flex flex-col overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                defaultValue: \"upload\",\n                                value: activeTab,\n                                onValueChange: setActiveTab,\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"upload\",\n                                                className: \"flex-1\",\n                                                children: \"Upload\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"palette\",\n                                                className: \"flex-1\",\n                                                children: \"Palette\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"upload\",\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleUploadClick,\n                                                    variant: \"default\",\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1205,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Upload Image\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1204,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    ref: fileInputRef,\n                                                    onChange: handleFileChange,\n                                                    accept: \"image/jpeg,image/png,image/webp,image/gif,image/bmp,image/tiff,image/svg+xml,image/x-icon,image/vnd.microsoft.icon,image/heic,image/heif,image/avif,image/jp2,image/jpx,image/jpm,image/jxl\",\n                                                    className: \"hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1217,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Extraction Method\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1218,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                                            defaultValue: \"ai\",\n                                                            className: \"w-full\",\n                                                            onValueChange: (value)=>handleExtractionMethodChange(value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                                                    className: \"grid w-full grid-cols-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                            value: \"ai\",\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1223,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \" AI\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1222,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                            value: \"manual\",\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1226,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \" Manual\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1225,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1221,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                    value: \"ai\",\n                                                                    className: \"mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mb-2\",\n                                                                            children: \"Extract multiple colors at once using AI.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1231,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        creditState.credits === 0 && !isAdminMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-3 border border-dashed rounded-md bg-muted/50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center mb-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-5 w-5 text-muted-foreground mb-2 animate-spin\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1237,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-center mb-1\",\n                                                                                            children: \"Out of credits\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1238,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        timeRemaining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-muted-foreground flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1241,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                \"Credits at \",\n                                                                                                timeRemaining\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1240,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1236,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: [\n                                                                                                \"Credits refill every \",\n                                                                                                (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditRegenerationPeriod)()\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1247,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            onClick: toggleAdminInput,\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            className: \"whitespace-nowrap\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                    className: \"mr-2 h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1256,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                \" Upgrade\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1250,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1246,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1235,\n                                                                            columnNumber: 29\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: handleExtract,\n                                                                            disabled: isExtracting || !selectedFile,\n                                                                            className: \"w-full\",\n                                                                            size: \"sm\",\n                                                                            children: isExtracting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loader__WEBPACK_IMPORTED_MODULE_8__.Loader, {\n                                                                                        size: \"sm\",\n                                                                                        className: \"mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1269,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \" Extracting...\"\n                                                                                ]\n                                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1273,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \" Extract Colors\"\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1261,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        extractionError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-red-500 mt-2\",\n                                                                            children: extractionError\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1279,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1230,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                    value: \"manual\",\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-3\",\n                                                                        children: creditState.credits === 0 && !isAdminMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-3 border border-dashed rounded-md bg-muted/50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center mb-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-5 w-5 text-muted-foreground mb-2 animate-spin\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1288,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-center mb-1\",\n                                                                                            children: \"Out of credits\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1289,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        timeRemaining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-muted-foreground flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1292,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                \"Credits at \",\n                                                                                                timeRemaining\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1291,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1287,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: [\n                                                                                                \"Credits refill every \",\n                                                                                                (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditRegenerationPeriod)()\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1298,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            onClick: toggleAdminInput,\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            className: \"whitespace-nowrap\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                    className: \"mr-2 h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1307,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                \" Upgrade\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1301,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1297,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1286,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-3 h-3 rounded-full mr-2 bg-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1314,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs font-medium\",\n                                                                                            children: \"Ready to pick colors\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1315,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1313,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: \"Hover over the image to see a magnified view, then click to extract any color you want.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1320,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                currentPixelColor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"p-2 border rounded-md bg-card\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs mb-1 text-muted-foreground\",\n                                                                                            children: \"Current color:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1326,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-8 h-8 rounded-md border\",\n                                                                                                    style: {\n                                                                                                        backgroundColor: currentPixelColor\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1328,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-xs font-mono\",\n                                                                                                    children: currentPixelColor\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1332,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1327,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1325,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"border-t pt-2 mt-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-[10px] text-muted-foreground\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium\",\n                                                                                                children: \"Tip:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                lineNumber: 1339,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            \" You can pick multiple colors without having to reselect this tab each time.\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1338,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1337,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1284,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1283,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1220,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        imageSize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Image size: \",\n                                                                    Math.round(imageSize.width),\n                                                                    \" \\xd7 \",\n                                                                    Math.round(imageSize.height)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1350,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1349,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1216,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1203,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"palette\",\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: extractedColors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Extracted Colors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    !isAdminMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: [\n                                                                            extractedColors.length,\n                                                                            \"/\",\n                                                                            COLOR_LIMIT_STANDARD\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1368,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: clearAllColors,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1378,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1372,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1366,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1364,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 max-h-[500px] overflow-y-auto pr-1\",\n                                                        children: extractedColors.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-8 w-8 rounded-md mr-2 border\",\n                                                                        style: {\n                                                                            backgroundColor: color.hex\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1385,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm font-medium truncate\",\n                                                                                children: color.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1390,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: color.hex\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1391,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1389,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-8 w-8\",\n                                                                                onClick: ()=>copyColor(color.hex),\n                                                                                children: copiedColor === color.hex ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1400,\n                                                                                    columnNumber: 62\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1400,\n                                                                                    columnNumber: 94\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1394,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                                onClick: ()=>removeColor(index),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1408,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1402,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1393,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1384,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            onClick: handleShowCodePreview,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1416,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" View Code\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1415,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1414,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center justify-center p-8 text-center text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-12 w-12 mb-4 opacity-20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1422,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-2\",\n                                                        children: \"No colors extracted yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1423,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Upload an image and use AI or the color picker to extract colors\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1424,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1421,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1361,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1360,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 1196,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 1195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 p-6 overflow-y-auto\",\n                            children: [\n                                selectedImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                            ref: canvasRef,\n                                            onClick: handleCanvasClick,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseEnter: handleMouseEnter,\n                                            onMouseLeave: handleMouseLeave,\n                                            className: \"border border-border rounded-lg max-w-full shadow-md \".concat(isPickingColor ? \"cursor-crosshair\" : \"\"),\n                                            style: {\n                                                maxHeight: \"70vh\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1436,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"destructive\",\n                                            size: \"icon\",\n                                            className: \"absolute top-2 right-2 h-8 w-8 shadow-md hover:bg-red-600 z-10\",\n                                            onClick: removeImage,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1453,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1447,\n                                            columnNumber: 19\n                                        }, this),\n                                        isPickingColor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 left-3 bg-background/90 text-foreground px-3 py-1.5 rounded-md text-sm shadow-xl border border-border\",\n                                            children: \"Click anywhere on the image to extract a color\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1457,\n                                            columnNumber: 19\n                                        }, this),\n                                        isPickingColor && showMagnifier && mousePosition && canvasRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pointer-events-none fixed\",\n                                            style: {\n                                                position: 'absolute',\n                                                left: getMagnifierPosition(mousePosition.x, mousePosition.y, canvasRef.current.getBoundingClientRect(), 150).left,\n                                                top: getMagnifierPosition(mousePosition.x, mousePosition.y, canvasRef.current.getBoundingClientRect(), 150).top,\n                                                zIndex: 50\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_magnifier__WEBPACK_IMPORTED_MODULE_11__.Magnifier, {\n                                                sourceCanvas: canvasRef.current,\n                                                x: mousePosition.x,\n                                                y: mousePosition.y,\n                                                zoomLevel: 5,\n                                                size: 150\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1471,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1462,\n                                            columnNumber: 19\n                                        }, this),\n                                        isExtracting && extractionMethod === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-background/70 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg border border-border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"h-12 w-12 text-primary animate-pulse mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1485,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-6 w-6 rounded-full border-2 border-primary border-t-transparent animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1487,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1486,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1484,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-2\",\n                                                    children: \"AI Analyzing Image\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1490,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-muted-foreground mb-4 max-w-md px-4\",\n                                                    children: \"Extracting the most prominent and visually important colors in your image...\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1491,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1483,\n                                            columnNumber: 19\n                                        }, this),\n                                        creditState.credits === 0 && !isAdminMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-background/80 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg border border-border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-12 w-12 text-muted-foreground mb-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1499,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-2\",\n                                                    children: \"Credits Depleted\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1500,\n                                                    columnNumber: 21\n                                                }, this),\n                                                timeRemaining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-muted-foreground mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1503,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Credits available at \",\n                                                                timeRemaining\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1504,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1502,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-muted-foreground mb-4 max-w-md px-4\",\n                                                    children: [\n                                                        \"Credits automatically refill every \",\n                                                        (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditRegenerationPeriod)(),\n                                                        \", or you can upgrade now for unlimited access.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1507,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            onClick: showCreditStatus,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1512,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" Check Status\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1511,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: toggleAdminInput,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1515,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" Upgrade Now\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1514,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1510,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1498,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1435,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center h-full text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-12 border-2 border-dashed border-border rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 text-muted-foreground mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1524,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"No Image Selected\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1525,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground mb-4 max-w-md\",\n                                                children: \"Upload an image to extract colors using AI or manually pick colors with the eyedropper tool.\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1526,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>{\n                                                    handleUploadClick();\n                                                    setActiveTab(\"upload\");\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1533,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Upload Image\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1529,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1523,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1522,\n                                    columnNumber: 15\n                                }, this),\n                                extractedColors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Extracted Color Palette\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                            children: extractedColors.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"overflow-hidden border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-28\",\n                                                            style: {\n                                                                backgroundColor: color.hex\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1546,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: color.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1553,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: color.hex\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1554,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1552,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"icon\",\n                                                                                            onClick: ()=>copyColor(color.hex),\n                                                                                            children: copiedColor === color.hex ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                lineNumber: 1560,\n                                                                                                columnNumber: 64\n                                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                lineNumber: 1560,\n                                                                                                columnNumber: 96\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1559,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1558,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                                                        side: \"left\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Copy color\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1564,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1563,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1557,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"icon\",\n                                                                                            onClick: ()=>removeColor(index),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                lineNumber: 1570,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1569,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1568,\n                                                                                        columnNumber: 23\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                                                        side: \"left\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Remove color\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1574,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1573,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1567,\n                                                                                columnNumber: 21\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1556,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1551,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1550,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1545,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1543,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1541,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 1433,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 1193,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"border-t p-2 text-center text-xs text-muted-foreground\",\n                    children: \"Coloriqo - The right color tool\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 1589,\n                    columnNumber: 9\n                }, this),\n                showCodePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_code_preview__WEBPACK_IMPORTED_MODULE_10__.CodePreview, {\n                    colors: allColors,\n                    onClose: ()=>setShowCodePreview(false)\n                }, void 0, false, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 1595,\n                    columnNumber: 11\n                }, this),\n                showAdminInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center\",\n                    onClick: (e)=>{\n                        // Close dialog when clicking on backdrop (outside the modal)\n                        if (e.target === e.currentTarget) {\n                            setShowAdminInput(false);\n                        }\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-card border rounded-lg shadow-lg w-96 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"Enter Admin Code\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 1613,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: adminCodeInput,\n                                        onChange: (e)=>setAdminCodeInput(e.target.value),\n                                        placeholder: \"Enter admin code...\",\n                                        className: \"w-full p-2 border rounded-md bg-background text-foreground\",\n                                        onKeyDown: (e)=>{\n                                            if (e.key === 'Enter') {\n                                                verifyAdminCode(adminCodeInput);\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1615,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setShowAdminInput(false),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1628,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>verifyAdminCode(adminCodeInput),\n                                                children: \"Submit\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1634,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1627,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 1614,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                        lineNumber: 1612,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 1603,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n            lineNumber: 909,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n        lineNumber: 908,\n        columnNumber: 5\n    }, this);\n}\n_s(ColorCards, \"2Qt3I7IC0+MTP79ceKXJGM5WqOg=\", false, function() {\n    return [\n        _hooks_use_color_extractor__WEBPACK_IMPORTED_MODULE_7__.useColorExtractor\n    ];\n});\n_c = ColorCards;\nvar _c;\n$RefreshReg$(_c, \"ColorCards\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/color-cards.tsx\n"));

/***/ })

});