"use client"

import { useState, useRef, useEffect } from "react"
import { useTheme } from "next-themes"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { 
  Droplet, 
  Copy, 
  Zap, 
  PaintBucket, 
  Moon, 
  Sun, 
  Download,
  Cloud,
  Play,
  ArrowRight,
  X,
  Menu,
  type LucideProps
} from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

// Types
interface FeatureCardProps {
  icon: React.ReactNode
  title: string
  description: string
  index: number
  color: string
}

interface GradientBlobProps {
  className?: string
  style?: React.CSSProperties
}

// Custom Color Logo
function ColorLogo(props: LucideProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      strokeWidth="2"
      {...props}
    >
      <path 
        d="M12 2L2 7L12 12L22 7L12 2Z" 
        fill="currentColor" 
        stroke="currentColor"
      />
      <path 
        d="M2 17L12 22L22 17" 
        stroke="currentColor" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path 
        d="M2 12L12 17L22 12" 
        stroke="currentColor" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
    </svg>
  )
}

// Theme Toggle Component
function ThemeToggle() {
  const { setTheme, theme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="relative h-10 w-10 rounded-none border-2 border-black bg-white p-0 dark:border-white dark:bg-black">
          <Sun className="h-5 w-5 rotate-0 scale-100 text-black transition-all dark:-rotate-90 dark:scale-0 dark:text-white" />
          <Moon className="absolute h-5 w-5 rotate-90 scale-0 text-black transition-all dark:rotate-0 dark:scale-100 dark:text-white" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="rounded-none border-2 border-black bg-white font-mono text-black dark:border-white dark:bg-black dark:text-white">
        <DropdownMenuItem onClick={() => setTheme("light")} className="focus:bg-yellow-400 focus:text-black">LIGHT</DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")} className="focus:bg-indigo-500 focus:text-white">DARK</DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")} className="focus:bg-green-400 focus:text-black">SYSTEM</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Mobile Menu
function MobileMenu() {
  const [isOpen, setIsOpen] = useState(false)
  
  return (
    <div className="md:hidden">
      <Button 
        variant="outline" 
        size="icon" 
        className="relative h-10 w-10 rounded-none border-2 border-black bg-white p-0 dark:border-white dark:bg-black"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? (
          <X className="h-5 w-5 text-black dark:text-white" />
        ) : (
          <Menu className="h-5 w-5 text-black dark:text-white" />
        )}
      </Button>
      
      {isOpen && (
        <div className="absolute left-0 top-[72px] z-50 w-full border-y-2 border-black bg-white p-4 dark:border-white dark:bg-black">
          <div className="flex flex-col gap-4">
            <Link 
              href="#features"
              className="font-mono text-xl uppercase text-black transition-colors hover:text-pink-600 dark:text-white dark:hover:text-pink-400"
              onClick={() => setIsOpen(false)}
            >
              Features
            </Link>
            <Link 
              href="/"
              className="font-mono text-xl uppercase text-black transition-colors hover:text-pink-600 dark:text-white dark:hover:text-pink-400"
              onClick={() => setIsOpen(false)}
            >
              Pricing
            </Link>
            <Button 
              className="mt-2 rounded-none border-2 border-black bg-yellow-300 px-8 py-6 font-mono text-black hover:bg-yellow-400 dark:border-white dark:bg-pink-600 dark:text-white dark:hover:bg-pink-500"
              onClick={() => setIsOpen(false)}
            >
              GET STARTED
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

// Feature Card Component 
function FeatureCard({ icon, title, description, index, color }: FeatureCardProps) {
  const [hovered, setHovered] = useState(false)
  
  return (
    <div 
      className={cn(
        "group relative h-full transform-gpu transition-all duration-300",
        hovered ? "translate-x-0 translate-y-0" : index % 2 === 0 ? "-translate-x-2 translate-y-2" : "translate-x-2 translate-y-2"
      )}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      <div className="absolute inset-0 bg-black dark:bg-white" />
      <Card className={cn(
        "relative h-full rounded-none border-2 border-black p-4 transition-all dark:border-white",
        hovered ? "translate-x-0 translate-y-0" : index % 2 === 0 ? "translate-x-2 translate-y-2" : "-translate-x-2 translate-y-2",
        color
      )}>
        <div className="flex h-full flex-col justify-between gap-4 p-2">
          <div className="flex items-start justify-between">
            <div className="rounded-none border-2 border-black bg-white p-2 dark:border-white dark:bg-black">
              {icon}
            </div>
            <div className="font-mono text-3xl font-bold">{String(index + 1).padStart(2, '0')}</div>
          </div>
          
          <div>
            <h3 className="mb-2 font-mono text-xl font-bold uppercase text-black dark:text-white">
              {title}
            </h3>
            <p className="text-black dark:text-white">
              {description}
            </p>
          </div>
        </div>
      </Card>
    </div>
  )
}

// Video Player Component
function VideoPlayer() {
  const [isPlaying, setIsPlaying] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  
  const handlePlayClick = () => {
    setIsPlaying(true)
    if (videoRef.current) {
      videoRef.current.play()
    }
  }
  
  return (
    <div className="group relative h-full w-full">
      <div className="absolute inset-0 bg-black dark:bg-white" />
      <div className="relative border-2 border-black bg-white transition-all group-hover:translate-x-1 group-hover:translate-y-1 dark:border-white dark:bg-black">
        <div className="relative aspect-video w-full">
          {isPlaying ? (
            <video 
              ref={videoRef}
              className="h-full w-full"
              controls
              src="/demo-video.mp4" // Replace with your actual video path
            >
              Your browser does not support the video tag.
            </video>
          ) : (
            <div className="flex h-full items-center justify-center bg-gradient-to-b from-pink-500 to-yellow-500 dark:from-indigo-600 dark:to-pink-600">
              <button 
                onClick={handlePlayClick}
                className="rounded-none border-2 border-black bg-white p-4 font-mono text-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transition-all hover:translate-x-[2px] hover:translate-y-[2px] hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] dark:border-white dark:bg-black dark:text-white dark:shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] dark:hover:shadow-[2px_2px_0px_0px_rgba(255,255,255,1)]"
              >
                <Play className="h-8 w-8 fill-current" />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Marquee Text Animation
function MarqueeText() {
  return (
    <div className="relative flex w-full overflow-hidden border-y-2 border-black bg-yellow-300 py-2 dark:border-white dark:bg-pink-600">
      <div className="animate-marquee whitespace-nowrap">
        <span className="mx-4 text-2xl font-black uppercase text-black dark:text-white">Extract Colors</span>
        <span className="mx-4 text-2xl font-black uppercase text-black dark:text-white">• AI-Powered •</span>
        <span className="mx-4 text-2xl font-black uppercase text-black dark:text-white">Perfect Palettes</span>
        <span className="mx-4 text-2xl font-black uppercase text-black dark:text-white">• One-Click Export •</span>
      </div>
      <div className="absolute top-0 animate-marquee2 whitespace-nowrap">
        <span className="mx-4 text-2xl font-black uppercase text-black dark:text-white">Extract Colors</span>
        <span className="mx-4 text-2xl font-black uppercase text-black dark:text-white">• AI-Powered •</span>
        <span className="mx-4 text-2xl font-black uppercase text-black dark:text-white">Perfect Palettes</span>
        <span className="mx-4 text-2xl font-black uppercase text-black dark:text-white">• One-Click Export •</span>
      </div>
    </div>
  )
}

// Random Dot Grid
function DotGrid() {
  const [dots, setDots] = useState<Array<{id: number, x: number, y: number, color: string}>>([])
  
  useEffect(() => {
    const colors = ['#FF0080', '#7928CA', '#FF4D4D', '#0070F3', '#00DFD8']
    const newDots = []
    const rows = 25
    const cols = 25
    
    for (let i = 0; i < rows; i++) {
      for (let j = 0; j < cols; j++) {
        if (Math.random() > 0.85) {
          newDots.push({
            id: i * cols + j,
            x: j * 20,
            y: i * 20,
            color: colors[Math.floor(Math.random() * colors.length)]
          })
        }
      }
    }
    
    setDots(newDots)
  }, [])

  return (
    <div className="absolute left-0 top-0 -z-10 h-full w-full overflow-hidden opacity-40 dark:opacity-30">
      <svg width="100%" height="100%" className="absolute inset-0">
        {dots.map((dot) => (
          <circle 
            key={dot.id}
            cx={dot.x} 
            cy={dot.y} 
            r="2" 
            fill={dot.color}
          />
        ))}
      </svg>
    </div>
  )
}

export default function NeoBrutalistLandingPage() {
  const [mounted, setMounted] = useState(false)
  const [cursorPos, setCursorPos] = useState({ x: 0, y: 0 })
  
  useEffect(() => {
    setMounted(true)
    
    const handleMouseMove = (e: MouseEvent) => {
      setCursorPos({ x: e.clientX, y: e.clientY })
    }
    
    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])
  
  // Features data
  const features = [
    {
      icon: <Zap className="h-6 w-6" />,
      title: "AI-Powered Extraction",
      description: "Extract perfect color palettes from any image using advanced AI technology.",
      color: "bg-pink-300 dark:bg-indigo-800"
    },
    {
      icon: <PaintBucket className="h-6 w-6" />,
      title: "Precision Control",
      description: "Fine-tune your color selections with professional-grade precision tools.",
      color: "bg-yellow-300 dark:bg-pink-800"
    },
    {
      icon: <Droplet className="h-6 w-6" />,
      title: "Color Harmonies",
      description: "Automatically generate complementary and analogous color schemes.",
      color: "bg-green-300 dark:bg-yellow-800"
    },
    {
      icon: <Copy className="h-6 w-6" />,
      title: "One-Click Export",
      description: "Copy colors in multiple formats or export complete palettes with a single click.",
      color: "bg-blue-300 dark:bg-green-800"
    },
    {
      icon: <Cloud className="h-6 w-6" />,
      title: "Cloud Storage",
      description: "Save and access your palettes from anywhere with secure cloud storage.",
      color: "bg-indigo-300 dark:bg-blue-800"
    },
    {
      icon: <Download className="h-6 w-6" />,
      title: "Multi-Format Support",
      description: "Export your palettes for use in all major design and development tools.",
      color: "bg-purple-300 dark:bg-purple-800"
    }
  ]

  if (!mounted) {
    return null
  }

  return (
    <div className="relative min-h-screen bg-white font-sans text-black dark:bg-black dark:text-white">
      {/* Custom mouse cursor */}
      <div 
        className="pointer-events-none fixed left-0 top-0 z-50 hidden h-8 w-8 mix-blend-difference md:block" 
        style={{ 
          transform: `translate(${cursorPos.x}px, ${cursorPos.y}px) translate(-50%, -50%)`,
          backgroundImage: 'radial-gradient(circle, white 40%, transparent 40%)'
        }}
      />
      
      {/* Background dot grid */}
      <DotGrid />

      {/* Marquee Banner */}
      <MarqueeText />

      {/* Header */}
      <header className="border-b-2 border-black px-4 py-4 dark:border-white">
        <div className="mx-auto flex max-w-7xl items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex h-10 w-10 items-center justify-center rounded-none border-2 border-black bg-yellow-300 dark:border-white dark:bg-pink-600">
              <ColorLogo className="h-6 w-6 text-black dark:text-white" />
            </div>
            <h1 className="font-mono text-2xl font-bold uppercase tracking-tighter text-black dark:text-white">
              <Link href="/" data-barba="wrapper">Coloriqo</Link>
            </h1>
          </div>
          
          <nav className="hidden items-center gap-8 md:flex">
            <Link 
              href="#features"
              className="font-mono uppercase text-black transition-colors hover:text-pink-600 dark:text-white dark:hover:text-pink-400"
            >
              Features
            </Link>
            <Link 
              href="/"
              className="font-mono uppercase text-black transition-colors hover:text-pink-600 dark:text-white dark:hover:text-pink-400"
              data-barba="wrapper"
            >
              Pricing
            </Link>
            <ThemeToggle />
            <Button className="rounded-none border-2 border-black bg-yellow-300 px-6 font-mono text-black hover:bg-yellow-400 dark:border-white dark:bg-pink-600 dark:text-white dark:hover:bg-pink-500">
              <Link href="/tool" data-barba="wrapper" className="flex items-center">GET STARTED</Link>
            </Button>
          </nav>
          
          <div className="flex items-center gap-4 md:hidden">
            <ThemeToggle />
            <MobileMenu />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="px-4 py-12">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 md:gap-12">
            <div className="md:pr-8">
              <div className="mb-8 inline-block rounded-none border-2 border-black bg-yellow-300 px-4 py-2 font-mono text-sm font-bold uppercase text-black dark:border-white dark:bg-pink-600 dark:text-white">
                New: AI Color Generation
              </div>
              
              <h1 className="mb-4 font-mono text-5xl font-black uppercase tracking-tight text-black dark:text-white md:text-6xl lg:text-7xl">
                Colors <br/>
                <span className="text-pink-600 dark:text-yellow-300">Extracted</span> <br/>
                Instantly
              </h1>
              
              <p className="mb-8 max-w-lg text-xl text-black dark:text-white">
                Extract perfect color palettes from any image using AI. Built for designers, developers, and creatives who need the perfect colors.
              </p>
              
              <div className="flex flex-col gap-4 sm:flex-row">
                <Button className="group relative rounded-none border-2 border-black bg-pink-600 px-8 py-6 font-mono text-lg font-bold uppercase text-white transition-all hover:translate-x-0 hover:translate-y-0 hover:bg-pink-500 dark:border-white dark:bg-yellow-300 dark:text-black dark:hover:bg-yellow-400">
                  <Link href="/tool" data-barba="wrapper" className="relative z-10 flex items-center gap-2">
                    Try it free <ArrowRight className="h-5 w-5" />
                  </Link>
                </Button>
                
                <Button variant="outline" className="rounded-none border-2 border-black bg-transparent px-8 py-6 font-mono text-lg font-bold uppercase text-black transition-all hover:bg-black hover:text-white dark:border-white dark:text-white dark:hover:bg-white dark:hover:text-black">
                  Watch Demo
                </Button>
              </div>
            </div>
            
            <div className="relative">
              <VideoPlayer />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="px-4 py-16">
        <div className="mx-auto max-w-7xl">
          <div className="mb-16 max-w-2xl">
            <div className="mb-4 inline-block rounded-none border-2 border-black bg-yellow-300 px-4 py-2 font-mono text-sm font-bold uppercase text-black dark:border-white dark:bg-pink-600 dark:text-white">
              Features
            </div>
            <h2 className="mb-6 font-mono text-4xl font-black uppercase tracking-tight text-black dark:text-white md:text-5xl">
              Built for<br/>
              Color Enthusiasts
            </h2>
            <p className="text-xl text-black dark:text-white">
              Everything you need to extract, manipulate, and manage perfect color palettes
            </p>
          </div>
          
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {features.map((feature, index) => (
              <FeatureCard 
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                index={index}
                color={feature.color}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Call To Action */}
      <section className="border-y-2 border-black bg-yellow-300 px-4 py-16 dark:border-white dark:bg-pink-600">
        <div className="mx-auto max-w-7xl">
          <div className="flex flex-col items-center justify-between gap-8 md:flex-row md:gap-4">
            <div>
              <h2 className="font-mono text-3xl font-black uppercase text-black dark:text-white md:text-4xl">
                Ready to Extract<br/>
                Perfect Colors?
              </h2>
            </div>
            
            <Button className="relative rounded-none border-2 border-black bg-black px-8 py-6 font-mono text-lg font-bold uppercase text-white transition-all hover:bg-white hover:text-black dark:border-white dark:bg-white dark:text-black dark:hover:bg-black dark:hover:text-white">
              <Link href="/tool" data-barba="wrapper" className="relative z-10 flex items-center gap-2">
                Start Now <ArrowRight className="h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-b-2 border-black bg-white px-4 py-12 dark:border-white dark:bg-black">
        <div className="mx-auto max-w-7xl">
          <div className="flex flex-col justify-between gap-8 md:flex-row md:gap-4">
            <div className="flex items-center gap-2">
              <div className="flex h-10 w-10 items-center justify-center rounded-none border-2 border-black bg-yellow-300 dark:border-white dark:bg-pink-600">
                <ColorLogo className="h-6 w-6 text-black dark:text-white" />
              </div>
              <span className="font-mono text-xl font-bold uppercase tracking-tighter text-black dark:text-white">
                Coloriqo
              </span>
            </div>
            
            <div className="flex flex-wrap gap-x-8 gap-y-4">
              <Link href="/" className="font-mono uppercase text-black hover:underline dark:text-white" data-barba="wrapper">
                Home
              </Link>
              <Link href="#features" className="font-mono uppercase text-black hover:underline dark:text-white" data-barba="wrapper">
                Features
              </Link>
              <Link href="/" className="font-mono uppercase text-black hover:underline dark:text-white" data-barba="wrapper">
                Pricing
              </Link>
              <Link href="/" className="font-mono uppercase text-black hover:underline dark:text-white" data-barba="wrapper">
                Contact
              </Link>
            </div>
          </div>
          
          <div className="mt-8 border-t-2 border-black pt-8 dark:border-white">
            <p className="font-mono text-sm uppercase text-black dark:text-white">
              © {new Date().getFullYear()} Coloriqo. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
} 