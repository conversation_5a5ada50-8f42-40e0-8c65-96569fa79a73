"use client";

import { cn } from "@/lib/utils";
import React, { useState, useEffect } from "react";
import { useTheme } from "next-themes";

/**
 * InteractiveGridPattern is a component that renders a grid pattern with interactive squares.
 *
 * @param width - The width of each square.
 * @param height - The height of each square.
 * @param squares - The number of squares in the grid. The first element is the number of horizontal squares, and the second element is the number of vertical squares.
 * @param className - The class name of the grid.
 * @param squaresClassName - The class name of the squares.
 */
interface InteractiveGridPatternProps extends React.SVGProps<SVGSVGElement> {
  width?: number;
  height?: number;
  squares?: [number, number]; // [horizontal, vertical]
  className?: string;
  squaresClassName?: string;
}

/**
 * The InteractiveGridPattern component.
 *
 * @see InteractiveGridPatternProps for the props interface.
 * @returns A React component.
 */
export const InteractiveGridPattern = React.memo(function InteractiveGridPattern({
  width = 40,
  height = 40,
  squares = [24, 24],
  className,
  squaresClassName,
  ...props
}: InteractiveGridPatternProps) {
  const [horizontal, vertical] = squares;
  const [hoveredSquare, setHoveredSquare] = useState<number | null>(null);
  const [colors, setColors] = useState<string[]>([]);
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Color palettes for light and dark modes
  const lightModeColors = [
    "#FF5E5B", "#D8A7B1", "#B28DFF", "#6EB5FF", "#42E2B8", 
    "#F7D002", "#FF9966", "#FF6B6B", "#C04CFD", "#01BAEF",
  ];

  const darkModeColors = [
    "#FF7D7A", "#F0B7C2", "#C9A9FF", "#8ECCFF", "#65F5CB", 
    "#FFE03D", "#FFBB8F", "#FF8F8F", "#D67DFF", "#33D6FF",
  ];

  // Set mounted state and generate initial colors
  useEffect(() => {
    setMounted(true);

    // Generate random colors for the grid
    const colorPalette = resolvedTheme === "dark" ? darkModeColors : lightModeColors;
    const newColors = Array.from({ length: horizontal * vertical }, () => {
      return colorPalette[Math.floor(Math.random() * colorPalette.length)];
    });
    setColors(newColors);
  }, []);

  // Update colors when theme or dimensions change
  useEffect(() => {
    if (!mounted) return;
    
    const colorPalette = resolvedTheme === "dark" ? darkModeColors : lightModeColors;
    const newColors = Array.from({ length: horizontal * vertical }, () => {
      return colorPalette[Math.floor(Math.random() * colorPalette.length)];
    });
    setColors(newColors);
  }, [resolvedTheme, mounted, horizontal, vertical]);

  // Memoize the squares to prevent unnecessary re-renders
  const squares_array = React.useMemo(() => {
    return Array.from({ length: horizontal * vertical }).map((_, index) => {
      const x = (index % horizontal) * width;
      const y = Math.floor(index / horizontal) * height;
      
      // Calculate distance from hovered square for gradient effect
      let opacity = 0.05;
      if (hoveredSquare !== null) {
        const hoveredCol = hoveredSquare % horizontal;
        const hoveredRow = Math.floor(hoveredSquare / horizontal);
        const currentCol = index % horizontal;
        const currentRow = Math.floor(index / horizontal);
        
        const distance = Math.sqrt(
          Math.pow(hoveredCol - currentCol, 2) + 
          Math.pow(hoveredRow - currentRow, 2)
        );
        
        // Create a gradient effect based on distance
        opacity = Math.max(0.05, 1 - distance / 5);
      }

      return (
        <rect
          key={index}
          x={x}
          y={y}
          width={width}
          height={height}
          className={cn(
            "transition-all duration-300 ease-in-out [&:not(:hover)]:duration-1000",
            squaresClassName,
          )}
          style={{
            fill: colors[index] || "#000000",
            fillOpacity: hoveredSquare === index ? 0.7 : opacity,
            stroke: "rgba(255, 255, 255, 0.1)",
          }}
          onMouseEnter={() => setHoveredSquare(index)}
          onMouseLeave={() => setHoveredSquare(null)}
        />
      );
    });
  }, [horizontal, vertical, width, height, colors, hoveredSquare, squaresClassName]);

  return (
    <svg
      width={width * horizontal}
      height={height * vertical}
      className={cn(
        "absolute inset-0 h-full w-full border border-gray-400/30",
        className,
      )}
      {...props}
    >
      {squares_array}
    </svg>
  );
});
