import { Color } from "@/types";
import { getColorName } from "./utils";

/**
 * Parses colors from Gemini API response text
 */
export async function parseColorsFromResponse(text: string): Promise<Color[]> {
  // Extract JSON array from the response if needed
  const jsonMatch = text.match(/\[[\s\S]*\]/);
  const colorsJson = jsonMatch ? jsonMatch[0] : text;
  
  try {
    const colors = JSON.parse(colorsJson);
    
    // Validate each color has name and hex properties
    const validColors = colors.filter((color: any) => 
      color && typeof color.name === "string" && typeof color.hex === "string"
    );
    
    // Add tailwind property for each color
    return validColors.map((color: any) => ({
      ...color,
      tailwind: color.name.toLowerCase().replace(/\s+/g, "-")
    }));
  } catch (e) {
    console.error("Failed to parse colors:", e);
    throw new Error(`Failed to parse color data: ${text}`);
  }
}

/**
 * Generates a prompt for Gemini API to extract colors
 */
export function generateColorExtractionPrompt(existingColors: string[] = [], maxColors: number = 5): string {
  let prompt = `Analyze this image and extract up to ${maxColors} most prominent colors. For each color, provide the name and hex code. Format your response as a JSON array of objects with 'name' and 'hex' properties. Example: [{\"name\":\"Blue\",\"hex\":\"#0000FF\"},{\"name\":\"Red\",\"hex\":\"#FF0000\"}]. Only return the JSON array, nothing else.`;
  
  // If we have existing colors, ask for different colors
  if (existingColors.length > 0) {
    prompt = `Analyze this image and extract up to ${maxColors} unique and visually distinct colors that are NOT similar to the following colors already in the palette: ${existingColors.join(', ')}. 
    
Look for colors that add diversity to the palette and represent different areas of the image. For each color, provide the name and hex code. Format your response as a JSON array of objects with 'name' and 'hex' properties. Example: [{\"name\":\"Blue\",\"hex\":\"#0000FF\"},{\"name\":\"Red\",\"hex\":\"#FF0000\"}]. Only return the JSON array, nothing else.`;
  }
  
  return prompt;
}

/**
 * Formats color for display
 */
export function formatColorForDisplay(color: Color): Color {
  // Ensure hex code is uppercase and has # prefix
  let hex = color.hex.toUpperCase();
  if (!hex.startsWith("#")) {
    hex = `#${hex}`;
  }
  
  // Generate name if not provided
  const name = color.name || getColorName(hex);
  
  // Generate tailwind class name if not provided
  const tailwind = color.tailwind || name.toLowerCase().replace(/\s+/g, "-");
  
  return {
    name,
    hex,
    tailwind
  };
}

/**
 * Generates a prompt for naming colors using AI
 */
export function generateColorNamingPrompt(colors: string[]): string {
  return `For each of the following hex colors, provide a descriptive, professional color name that would be suitable for a design palette. Return the results as a JSON array of objects with 'hex' and 'name' properties. ONLY return the JSON array.

Colors to name: ${colors.join(', ')}

Example response format:
[
  {"hex": "#FF0000", "name": "Crimson Red"},
  {"hex": "#0000FF", "name": "Royal Blue"}
]`;
} 