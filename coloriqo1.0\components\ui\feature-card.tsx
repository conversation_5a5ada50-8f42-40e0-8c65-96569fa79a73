import React from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  className?: string;
  iconColor?: string;
}

export function FeatureCard({ 
  icon: Icon, 
  title, 
  description, 
  className,
  iconColor = "text-primary" 
}: FeatureCardProps) {
  return (
    <Card className={cn("overflow-hidden transition-all duration-200 hover:shadow-md", className)}>
      <CardContent className="p-6">
        <div className="flex flex-col items-center text-center space-y-3">
          <div className={cn("p-3 rounded-full bg-primary/10", iconColor)}>
            <Icon className="h-6 w-6" />
          </div>
          <h3 className="font-semibold text-lg">{title}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
      </CardContent>
    </Card>
  );
} 