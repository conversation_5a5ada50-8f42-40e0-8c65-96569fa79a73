"use client";

import { useEffect, useRef, useState, Suspense } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { gsap } from "gsap";

interface PageTransitionProps {
  children: React.ReactNode;
  onTransitionRequested?: (callback: () => void) => void;
}

// Creative transition phrases
const transitionPhrases = [
  "Colorizing...",
  "Painting pixels...",
  "Blending hues...",
  "Crafting palettes...",
  "Mixing tones...",
  "Exploring spectrum...",
];

export function PageTransition({ children, onTransitionRequested }: PageTransitionProps) {
  return (
    <Suspense fallback={<div className="page-wrapper">{children}</div>}>
      <PageTransitionContent onTransitionRequested={onTransitionRequested}>{children}</PageTransitionContent>
    </Suspense>
  );
}

function PageTransitionContent({ children, onTransitionRequested }: PageTransitionProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [displayChildren, setDisplayChildren] = useState(children);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [nextPath, setNextPath] = useState<string | null>(null);
  const transitionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const initialized = useRef(false);
  const prevPathRef = useRef(pathname);

  // Track route changes for transitions
  useEffect(() => {
    setDisplayChildren(children);
  }, [children]);

  // Create transition container with optimized DOM structure
  useEffect(() => {
    if (typeof window === 'undefined' || initialized.current) return;

    // Create minimal DOM structure for better performance
    const container = document.createElement('div');
    container.className = 'transition-overlay';
    
    // Single background with gradient capability
    const background = document.createElement('div');
    background.className = 'transition-background';
    container.appendChild(background);
    
    // Create revamped SVG effect container using SVG for better performance
    const svgContainer = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svgContainer.setAttribute('class', 'transition-svg');
    svgContainer.setAttribute('viewBox', '0 0 100 100');
    svgContainer.setAttribute('preserveAspectRatio', 'none');
    
    // Create pattern elements
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    svgContainer.appendChild(defs);
    
    // Create path elements for animation
    for (let i = 0; i < 6; i++) {
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('class', `transition-path path-${i}`);
      path.setAttribute('d', `M0,${20 + i * 10} C20,${15 + i * 10} 40,${25 + i * 10} 100,${20 + i * 10}`);
      path.setAttribute('stroke', 'rgba(255,255,255,0.4)');
      path.setAttribute('stroke-width', '0.5');
      path.setAttribute('fill', 'none');
      svgContainer.appendChild(path);
    }
    
    container.appendChild(svgContainer);
    
    // Create text container
    const textContainer = document.createElement('div');
    textContainer.className = 'transition-text';
    
    // Create brand text
    const brand = document.createElement('div');
    brand.className = 'transition-brand';
    brand.innerHTML = 'Coloriqo';
    textContainer.appendChild(brand);
    
    // Create phrase text
    const phrase = document.createElement('div');
    phrase.className = 'transition-phrase';
    textContainer.appendChild(phrase);
    
    container.appendChild(textContainer);
    document.body.appendChild(container);
    
    transitionRef.current = container;
    initialized.current = true;
  }, []);
  
  // Faster, optimized transition animation
  const runEnterTransition = (callback?: () => void) => {
    if (!transitionRef.current) return;
    
    // Set flag
    setIsTransitioning(true);
    
    const container = transitionRef.current;
    const phrase = container.querySelector('.transition-phrase');
    
    // Set random transition phrase
    if (phrase) {
      const randomPhrase = transitionPhrases[Math.floor(Math.random() * transitionPhrases.length)];
      phrase.textContent = randomPhrase;
    }

    // Set initial state - everything reset and hidden
    gsap.set(container, { 
      visibility: 'visible', 
      display: 'flex'
    });
    
    // Hide content immediately to prevent flickering
    if (contentRef.current) {
      gsap.to(contentRef.current, { opacity: 0, duration: 0.2 });
    }
    
    // Fast, optimized timeline - reduced duration for speed
    const tl = gsap.timeline({
      defaults: { ease: 'power2.out' }
    });
    
    // Reveal background with quick clip animation from center
    tl.fromTo('.transition-background', 
      { clipPath: 'circle(0% at center)' },
      { clipPath: 'circle(100% at center)', duration: 0.4 }
    );
    
    // Animate SVG paths with staggered timing
    tl.fromTo('.transition-path', 
      { strokeDasharray: '100%', strokeDashoffset: '100%', opacity: 0 },
      { strokeDashoffset: '0%', opacity: 0.8, duration: 0.6, stagger: 0.05 },
      0.1
    );
    
    // Quick text animation
    tl.fromTo('.transition-brand', 
      { opacity: 0, y: -20 },
      { opacity: 1, y: 0, duration: 0.3 },
      0.2
    );
    
    tl.fromTo('.transition-phrase', 
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.3 },
      0.3
    );
    
    // Add minimal pause before callback - just enough for visual effect
    tl.to({}, { duration: 0.2 });
    
    // Add callback to the end
    if (callback) {
      tl.eventCallback("onComplete", callback);
    }
    
    return tl;
  };
  
  // Fast exit transition
  const runExitTransition = (callback?: () => void) => {
    if (!transitionRef.current) return;
    
    const container = transitionRef.current;
    
    // Fast reverse animation
    const tl = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false);
        gsap.set(container, { visibility: 'hidden' });
        
        // Ensure content is visible
        if (contentRef.current) {
          gsap.set(contentRef.current, { opacity: 1, visibility: 'visible' });
        }
        
        if (callback) callback();
      }
    });
    
    // Quick fade out for text elements
    tl.to(['.transition-brand', '.transition-phrase'], { 
      opacity: 0, 
      y: -10, 
      duration: 0.25,
      stagger: 0.05
    });
    
    // Reverse SVG paths
    tl.to('.transition-path', {
      opacity: 0,
      duration: 0.2
    }, 0);
    
    // Quick circle close animation
    tl.to('.transition-background', { 
      clipPath: 'circle(0% at center)', 
      duration: 0.3 
    }, 0.1);
    
    return tl;
  };

  // Listen for navigation events
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleStartTransition = (e: Event) => {
      const event = e as CustomEvent;
      if (event.detail && event.detail.path) {
        setNextPath(event.detail.path);
        
        // Run optimized animation and handle callback immediately after main animation completes
        const timeline = runEnterTransition();
        
        if (timeline) {
          timeline.eventCallback("onComplete", () => {
            if (onTransitionRequested) {
              onTransitionRequested(() => {
                console.log("Navigation executing");
              });
            }
          });
        }
      }
    };
    
    document.addEventListener('startPageTransition', handleStartTransition);
    
    return () => {
      document.removeEventListener('startPageTransition', handleStartTransition);
    };
  }, [onTransitionRequested]);

  // Handle normal navigation
  useEffect(() => {
    if (!initialized.current || !transitionRef.current) return;

    // Skip initial load
    if (prevPathRef.current === pathname) {
      prevPathRef.current = pathname;
      return;
    }

    // Run entrance animation
    const tl1 = runEnterTransition();
    
    if (tl1) {
      tl1.then(() => {
        setDisplayChildren(children);
        
        // Quick delay then exit
        gsap.delayedCall(0.2, () => {
          runExitTransition();
        });
      });
    }
    
    prevPathRef.current = pathname;
  }, [pathname, searchParams, children]);
  
  // Handle manual navigation
  useEffect(() => {
    if (nextPath && contentRef.current) {
      setNextPath(null);
      
      // Quick exit transition
      gsap.delayedCall(0.1, () => {
        runExitTransition();
      });
    }
  }, [nextPath, children]);

  return (
    <div className="page-content" ref={contentRef}>
      {displayChildren}
    </div>
  );
}

export default PageTransition; 