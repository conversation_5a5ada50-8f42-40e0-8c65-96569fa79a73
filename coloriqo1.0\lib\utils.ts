import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

// Utility function for combining class names
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Convert RGB to HEX
export function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
}

// Convert HEX to RGB
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
}

// Convert RGB to HSL
export function rgbToHsl(r: number, g: number, b: number): { h: number; s: number; l: number } {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }

    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
}

// Color name mapping based on HSL values
export const COLOR_NAMES: Record<string, string> = {
  // Reds
  red: "#FF0000",
  crimson: "#DC143C",
  maroon: "#800000",
  tomato: "#FF6347",
  coral: "#FF7F50",
  salmon: "#FA8072",
  // Oranges
  orange: "#FFA500",
  gold: "#FFD700",
  amber: "#FFBF00",
  // Yellows
  yellow: "#FFFF00",
  khaki: "#F0E68C",
  lemon: "#FFF700",
  // Greens
  green: "#008000",
  lime: "#00FF00",
  olive: "#808000",
  teal: "#008080",
  emerald: "#50C878",
  mint: "#3EB489",
  sage: "#BCB88A",
  // Blues
  blue: "#0000FF",
  navy: "#000080",
  azure: "#007FFF",
  cyan: "#00FFFF",
  turquoise: "#40E0D0",
  skyblue: "#87CEEB",
  cobalt: "#0047AB",
  // Purples
  purple: "#800080",
  violet: "#8F00FF",
  magenta: "#FF00FF",
  lavender: "#E6E6FA",
  indigo: "#4B0082",
  // Browns
  brown: "#A52A2A",
  chocolate: "#D2691E",
  tan: "#D2B48C",
  beige: "#F5F5DC",
  // Neutrals
  black: "#000000",
  gray: "#808080",
  silver: "#C0C0C0",
  white: "#FFFFFF",
  ivory: "#FFFFF0",
  cream: "#FFFDD0",
};

// Get color name based on closest match
export function getColorName(hex: string): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return "Unknown";

  const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);

  // Determine brightness and saturation categories
  const brightness = hsl.l;
  const saturation = hsl.s;

  let prefix = "";
  let baseName = "";

  // Determine prefix based on lightness and saturation
  if (brightness < 20) prefix = "Dark ";
  else if (brightness > 80) prefix = "Light ";
  else if (saturation < 10) prefix = "Muted ";
  else if (saturation > 80) prefix = "Vibrant ";

  // Find the closest color name by comparing hex values
  let minDistance = Number.MAX_VALUE;

  for (const [name, colorHex] of Object.entries(COLOR_NAMES)) {
    const namedRgb = hexToRgb(colorHex);
    if (!namedRgb) continue;

    // Calculate color distance using simple Euclidean distance in RGB space
    const distance = Math.sqrt(
      Math.pow(namedRgb.r - rgb.r, 2) + Math.pow(namedRgb.g - rgb.g, 2) + Math.pow(namedRgb.b - rgb.b, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      baseName = name.charAt(0).toUpperCase() + name.slice(1);
    }
  }

  // If the color is very close to a named color, don't use a prefix
  if (minDistance < 30) {
    return baseName;
  }

  return prefix + baseName;
}
