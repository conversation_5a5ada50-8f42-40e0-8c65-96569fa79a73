
import type { Metadata } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON>ubi<PERSON>, Cinzel_Decorative } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/toaster'
import { PageTransitionWrapper } from '@/components/ui/page-transition-wrapper'

// Use Rubik for main headings (replacing Rubik Doodle Shadow)
const rubik = Rubik({ 
  subsets: ['latin'], 
  variable: '--font-rubik',
  weight: ['400', '500', '600', '700'],
  display: 'swap',
  preload: true
})

// Use Montserrat for secondary headings (replacing Foldit)
const montserrat = Montserrat({ 
  subsets: ['latin'],
  variable: '--font-montserrat',
  weight: ['400', '500', '600', '700', '800'],
  display: 'swap',
  preload: true
})

// Use Cinzel Decorative for elegant accents
const cinzelDecorative = Cinzel_Decorative({
  subsets: ['latin'],
  variable: '--font-cinzel',
  weight: ['400', '700', '900'],
  display: 'swap',
  preload: true
})

// Use Montserrat for body text
const montserratBody = Montserrat({ 
  subsets: ['latin'],
  variable: '--font-montserrat-body',
  weight: ['400', '500', '600', '700', '800'],
  display: 'swap',
  preload: true
})

export const metadata: Metadata = {
  title: 'Coloriqo | Unleash Your Color Story',
  description: "Transform visual inspiration into perfect color palettes with Coloriqo's AI-powered tools",
  keywords: ['color extraction', 'color palette', 'AI', 'design tools', 'Coloriqo'],
  icons: {
    icon: [
      {
        url: 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="%23a855f7" /><circle cx="50" cy="50" r="25" fill="%234f46e5" /></svg>',
        sizes: '32x32',
        type: 'image/svg+xml'
      }
    ]
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning className="scroll-smooth">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={`${montserratBody.variable} ${rubik.variable} ${montserrat.variable} ${cinzelDecorative.variable} font-sans`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <PageTransitionWrapper>
            {children}
          </PageTransitionWrapper>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}