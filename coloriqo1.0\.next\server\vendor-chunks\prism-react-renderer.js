"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prism-react-renderer";
exports.ids = ["vendor-chunks/prism-react-renderer"];
exports.modules = {

/***/ "(ssr)/./node_modules/prism-react-renderer/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/prism-react-renderer/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Highlight: () => (/* binding */ Highlight2),\n/* harmony export */   Prism: () => (/* binding */ Prism),\n/* harmony export */   normalizeTokens: () => (/* binding */ normalizeTokens_default),\n/* harmony export */   themes: () => (/* binding */ themes_exports),\n/* harmony export */   useTokenize: () => (/* binding */ useTokenize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n// ../../node_modules/.pnpm/prismjs@1.29.0_patch_hash=vrxx3pzkik6jpmgpayxfjunetu/node_modules/prismjs/prism.js\nvar require_prism = __commonJS({\n  \"../../node_modules/.pnpm/prismjs@1.29.0_patch_hash=vrxx3pzkik6jpmgpayxfjunetu/node_modules/prismjs/prism.js\"(exports, module) {\n    var Prism2 = function() {\n      var lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n      var uniqueId = 0;\n      var plainTextGrammar = {};\n      var _ = {\n        /**\n         * A namespace for utility methods.\n         *\n         * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n         * change or disappear at any time.\n         *\n         * @namespace\n         * @memberof Prism\n         */\n        util: {\n          encode: function encode(tokens) {\n            if (tokens instanceof Token) {\n              return new Token(tokens.type, encode(tokens.content), tokens.alias);\n            } else if (Array.isArray(tokens)) {\n              return tokens.map(encode);\n            } else {\n              return tokens.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/\\u00a0/g, \" \");\n            }\n          },\n          /**\n           * Returns the name of the type of the given value.\n           *\n           * @param {any} o\n           * @returns {string}\n           * @example\n           * type(null)      === 'Null'\n           * type(undefined) === 'Undefined'\n           * type(123)       === 'Number'\n           * type('foo')     === 'String'\n           * type(true)      === 'Boolean'\n           * type([1, 2])    === 'Array'\n           * type({})        === 'Object'\n           * type(String)    === 'Function'\n           * type(/abc+/)    === 'RegExp'\n           */\n          type: function(o) {\n            return Object.prototype.toString.call(o).slice(8, -1);\n          },\n          /**\n           * Returns a unique number for the given object. Later calls will still return the same number.\n           *\n           * @param {Object} obj\n           * @returns {number}\n           */\n          objId: function(obj) {\n            if (!obj[\"__id\"]) {\n              Object.defineProperty(obj, \"__id\", { value: ++uniqueId });\n            }\n            return obj[\"__id\"];\n          },\n          /**\n           * Creates a deep clone of the given object.\n           *\n           * The main intended use of this function is to clone language definitions.\n           *\n           * @param {T} o\n           * @param {Record<number, any>} [visited]\n           * @returns {T}\n           * @template T\n           */\n          clone: function deepClone(o, visited) {\n            visited = visited || {};\n            var clone;\n            var id;\n            switch (_.util.type(o)) {\n              case \"Object\":\n                id = _.util.objId(o);\n                if (visited[id]) {\n                  return visited[id];\n                }\n                clone = /** @type {Record<string, any>} */\n                {};\n                visited[id] = clone;\n                for (var key in o) {\n                  if (o.hasOwnProperty(key)) {\n                    clone[key] = deepClone(o[key], visited);\n                  }\n                }\n                return (\n                  /** @type {any} */\n                  clone\n                );\n              case \"Array\":\n                id = _.util.objId(o);\n                if (visited[id]) {\n                  return visited[id];\n                }\n                clone = [];\n                visited[id] = clone;\n                /** @type {Array} */\n                /** @type {any} */\n                o.forEach(function(v, i) {\n                  clone[i] = deepClone(v, visited);\n                });\n                return (\n                  /** @type {any} */\n                  clone\n                );\n              default:\n                return o;\n            }\n          },\n          /**\n           * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n           *\n           * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n           *\n           * @param {Element} element\n           * @returns {string}\n           */\n          getLanguage: function(element) {\n            while (element) {\n              var m = lang.exec(element.className);\n              if (m) {\n                return m[1].toLowerCase();\n              }\n              element = element.parentElement;\n            }\n            return \"none\";\n          },\n          /**\n           * Sets the Prism `language-xxxx` class of the given element.\n           *\n           * @param {Element} element\n           * @param {string} language\n           * @returns {void}\n           */\n          setLanguage: function(element, language) {\n            element.className = element.className.replace(RegExp(lang, \"gi\"), \"\");\n            element.classList.add(\"language-\" + language);\n          },\n          /**\n           * Returns whether a given class is active for `element`.\n           *\n           * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n           * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n           * given class is just the given class with a `no-` prefix.\n           *\n           * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n           * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n           * ancestors have the given class or the negated version of it, then the default activation will be returned.\n           *\n           * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n           * version of it, the class is considered active.\n           *\n           * @param {Element} element\n           * @param {string} className\n           * @param {boolean} [defaultActivation=false]\n           * @returns {boolean}\n           */\n          isActive: function(element, className, defaultActivation) {\n            var no = \"no-\" + className;\n            while (element) {\n              var classList = element.classList;\n              if (classList.contains(className)) {\n                return true;\n              }\n              if (classList.contains(no)) {\n                return false;\n              }\n              element = element.parentElement;\n            }\n            return !!defaultActivation;\n          }\n        },\n        /**\n         * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n         *\n         * @namespace\n         * @memberof Prism\n         * @public\n         */\n        languages: {\n          /**\n           * The grammar for plain, unformatted text.\n           */\n          plain: plainTextGrammar,\n          plaintext: plainTextGrammar,\n          text: plainTextGrammar,\n          txt: plainTextGrammar,\n          /**\n           * Creates a deep copy of the language with the given id and appends the given tokens.\n           *\n           * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n           * will be overwritten at its original position.\n           *\n           * ## Best practices\n           *\n           * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n           * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n           * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n           *\n           * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n           * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n           *\n           * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n           * @param {Grammar} redef The new tokens to append.\n           * @returns {Grammar} The new language created.\n           * @public\n           * @example\n           * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n           *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n           *     // at its original position\n           *     'comment': { ... },\n           *     // CSS doesn't have a 'color' token, so this token will be appended\n           *     'color': /\\b(?:red|green|blue)\\b/\n           * });\n           */\n          extend: function(id, redef) {\n            var lang2 = _.util.clone(_.languages[id]);\n            for (var key in redef) {\n              lang2[key] = redef[key];\n            }\n            return lang2;\n          },\n          /**\n           * Inserts tokens _before_ another token in a language definition or any other grammar.\n           *\n           * ## Usage\n           *\n           * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n           * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n           * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n           * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n           * this:\n           *\n           * ```js\n           * Prism.languages.markup.style = {\n           *     // token\n           * };\n           * ```\n           *\n           * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n           * before existing tokens. For the CSS example above, you would use it like this:\n           *\n           * ```js\n           * Prism.languages.insertBefore('markup', 'cdata', {\n           *     'style': {\n           *         // token\n           *     }\n           * });\n           * ```\n           *\n           * ## Special cases\n           *\n           * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n           * will be ignored.\n           *\n           * This behavior can be used to insert tokens after `before`:\n           *\n           * ```js\n           * Prism.languages.insertBefore('markup', 'comment', {\n           *     'comment': Prism.languages.markup.comment,\n           *     // tokens after 'comment'\n           * });\n           * ```\n           *\n           * ## Limitations\n           *\n           * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n           * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n           * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n           * deleting properties which is necessary to insert at arbitrary positions.\n           *\n           * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n           * Instead, it will create a new object and replace all references to the target object with the new one. This\n           * can be done without temporarily deleting properties, so the iteration order is well-defined.\n           *\n           * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n           * you hold the target object in a variable, then the value of the variable will not change.\n           *\n           * ```js\n           * var oldMarkup = Prism.languages.markup;\n           * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n           *\n           * assert(oldMarkup !== Prism.languages.markup);\n           * assert(newMarkup === Prism.languages.markup);\n           * ```\n           *\n           * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n           * object to be modified.\n           * @param {string} before The key to insert before.\n           * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n           * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n           * object to be modified.\n           *\n           * Defaults to `Prism.languages`.\n           * @returns {Grammar} The new grammar object.\n           * @public\n           */\n          insertBefore: function(inside, before, insert, root) {\n            root = root || /** @type {any} */\n            _.languages;\n            var grammar = root[inside];\n            var ret = {};\n            for (var token in grammar) {\n              if (grammar.hasOwnProperty(token)) {\n                if (token == before) {\n                  for (var newToken in insert) {\n                    if (insert.hasOwnProperty(newToken)) {\n                      ret[newToken] = insert[newToken];\n                    }\n                  }\n                }\n                if (!insert.hasOwnProperty(token)) {\n                  ret[token] = grammar[token];\n                }\n              }\n            }\n            var old = root[inside];\n            root[inside] = ret;\n            _.languages.DFS(_.languages, function(key, value) {\n              if (value === old && key != inside) {\n                this[key] = ret;\n              }\n            });\n            return ret;\n          },\n          // Traverse a language definition with Depth First Search\n          DFS: function DFS(o, callback, type, visited) {\n            visited = visited || {};\n            var objId = _.util.objId;\n            for (var i in o) {\n              if (o.hasOwnProperty(i)) {\n                callback.call(o, i, o[i], type || i);\n                var property = o[i];\n                var propertyType = _.util.type(property);\n                if (propertyType === \"Object\" && !visited[objId(property)]) {\n                  visited[objId(property)] = true;\n                  DFS(property, callback, null, visited);\n                } else if (propertyType === \"Array\" && !visited[objId(property)]) {\n                  visited[objId(property)] = true;\n                  DFS(property, callback, i, visited);\n                }\n              }\n            }\n          }\n        },\n        plugins: {},\n        /**\n         * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n         * and the language definitions to use, and returns a string with the HTML produced.\n         *\n         * The following hooks will be run:\n         * 1. `before-tokenize`\n         * 2. `after-tokenize`\n         * 3. `wrap`: On each {@link Token}.\n         *\n         * @param {string} text A string with the code to be highlighted.\n         * @param {Grammar} grammar An object containing the tokens to use.\n         *\n         * Usually a language definition like `Prism.languages.markup`.\n         * @param {string} language The name of the language definition passed to `grammar`.\n         * @returns {string} The highlighted HTML.\n         * @memberof Prism\n         * @public\n         * @example\n         * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n         */\n        highlight: function(text, grammar, language) {\n          var env = {\n            code: text,\n            grammar,\n            language\n          };\n          _.hooks.run(\"before-tokenize\", env);\n          if (!env.grammar) {\n            throw new Error('The language \"' + env.language + '\" has no grammar.');\n          }\n          env.tokens = _.tokenize(env.code, env.grammar);\n          _.hooks.run(\"after-tokenize\", env);\n          return Token.stringify(_.util.encode(env.tokens), env.language);\n        },\n        /**\n         * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n         * and the language definitions to use, and returns an array with the tokenized code.\n         *\n         * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n         *\n         * This method could be useful in other contexts as well, as a very crude parser.\n         *\n         * @param {string} text A string with the code to be highlighted.\n         * @param {Grammar} grammar An object containing the tokens to use.\n         *\n         * Usually a language definition like `Prism.languages.markup`.\n         * @returns {TokenStream} An array of strings and tokens, a token stream.\n         * @memberof Prism\n         * @public\n         * @example\n         * let code = `var foo = 0;`;\n         * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n         * tokens.forEach(token => {\n         *     if (token instanceof Prism.Token && token.type === 'number') {\n         *         console.log(`Found numeric literal: ${token.content}`);\n         *     }\n         * });\n         */\n        tokenize: function(text, grammar) {\n          var rest = grammar.rest;\n          if (rest) {\n            for (var token in rest) {\n              grammar[token] = rest[token];\n            }\n            delete grammar.rest;\n          }\n          var tokenList = new LinkedList();\n          addAfter(tokenList, tokenList.head, text);\n          matchGrammar(text, tokenList, grammar, tokenList.head, 0);\n          return toArray(tokenList);\n        },\n        /**\n         * @namespace\n         * @memberof Prism\n         * @public\n         */\n        hooks: {\n          all: {},\n          /**\n           * Adds the given callback to the list of callbacks for the given hook.\n           *\n           * The callback will be invoked when the hook it is registered for is run.\n           * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n           *\n           * One callback function can be registered to multiple hooks and the same hook multiple times.\n           *\n           * @param {string} name The name of the hook.\n           * @param {HookCallback} callback The callback function which is given environment variables.\n           * @public\n           */\n          add: function(name, callback) {\n            var hooks2 = _.hooks.all;\n            hooks2[name] = hooks2[name] || [];\n            hooks2[name].push(callback);\n          },\n          /**\n           * Runs a hook invoking all registered callbacks with the given environment variables.\n           *\n           * Callbacks will be invoked synchronously and in the order in which they were registered.\n           *\n           * @param {string} name The name of the hook.\n           * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n           * @public\n           */\n          run: function(name, env) {\n            var callbacks = _.hooks.all[name];\n            if (!callbacks || !callbacks.length) {\n              return;\n            }\n            for (var i = 0, callback; callback = callbacks[i++]; ) {\n              callback(env);\n            }\n          }\n        },\n        Token\n      };\n      function Token(type, content, alias, matchedStr) {\n        this.type = type;\n        this.content = content;\n        this.alias = alias;\n        this.length = (matchedStr || \"\").length | 0;\n      }\n      Token.stringify = function stringify(o, language) {\n        if (typeof o == \"string\") {\n          return o;\n        }\n        if (Array.isArray(o)) {\n          var s = \"\";\n          o.forEach(function(e) {\n            s += stringify(e, language);\n          });\n          return s;\n        }\n        var env = {\n          type: o.type,\n          content: stringify(o.content, language),\n          tag: \"span\",\n          classes: [\"token\", o.type],\n          attributes: {},\n          language\n        };\n        var aliases = o.alias;\n        if (aliases) {\n          if (Array.isArray(aliases)) {\n            Array.prototype.push.apply(env.classes, aliases);\n          } else {\n            env.classes.push(aliases);\n          }\n        }\n        _.hooks.run(\"wrap\", env);\n        var attributes = \"\";\n        for (var name in env.attributes) {\n          attributes += \" \" + name + '=\"' + (env.attributes[name] || \"\").replace(/\"/g, \"&quot;\") + '\"';\n        }\n        return \"<\" + env.tag + ' class=\"' + env.classes.join(\" \") + '\"' + attributes + \">\" + env.content + \"</\" + env.tag + \">\";\n      };\n      function matchPattern(pattern, pos, text, lookbehind) {\n        pattern.lastIndex = pos;\n        var match = pattern.exec(text);\n        if (match && lookbehind && match[1]) {\n          var lookbehindLength = match[1].length;\n          match.index += lookbehindLength;\n          match[0] = match[0].slice(lookbehindLength);\n        }\n        return match;\n      }\n      function matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n        for (var token in grammar) {\n          if (!grammar.hasOwnProperty(token) || !grammar[token]) {\n            continue;\n          }\n          var patterns = grammar[token];\n          patterns = Array.isArray(patterns) ? patterns : [patterns];\n          for (var j = 0; j < patterns.length; ++j) {\n            if (rematch && rematch.cause == token + \",\" + j) {\n              return;\n            }\n            var patternObj = patterns[j];\n            var inside = patternObj.inside;\n            var lookbehind = !!patternObj.lookbehind;\n            var greedy = !!patternObj.greedy;\n            var alias = patternObj.alias;\n            if (greedy && !patternObj.pattern.global) {\n              var flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n              patternObj.pattern = RegExp(patternObj.pattern.source, flags + \"g\");\n            }\n            var pattern = patternObj.pattern || patternObj;\n            for (var currentNode = startNode.next, pos = startPos; currentNode !== tokenList.tail; pos += currentNode.value.length, currentNode = currentNode.next) {\n              if (rematch && pos >= rematch.reach) {\n                break;\n              }\n              var str = currentNode.value;\n              if (tokenList.length > text.length) {\n                return;\n              }\n              if (str instanceof Token) {\n                continue;\n              }\n              var removeCount = 1;\n              var match;\n              if (greedy) {\n                match = matchPattern(pattern, pos, text, lookbehind);\n                if (!match || match.index >= text.length) {\n                  break;\n                }\n                var from = match.index;\n                var to = match.index + match[0].length;\n                var p = pos;\n                p += currentNode.value.length;\n                while (from >= p) {\n                  currentNode = currentNode.next;\n                  p += currentNode.value.length;\n                }\n                p -= currentNode.value.length;\n                pos = p;\n                if (currentNode.value instanceof Token) {\n                  continue;\n                }\n                for (var k = currentNode; k !== tokenList.tail && (p < to || typeof k.value === \"string\"); k = k.next) {\n                  removeCount++;\n                  p += k.value.length;\n                }\n                removeCount--;\n                str = text.slice(pos, p);\n                match.index -= pos;\n              } else {\n                match = matchPattern(pattern, 0, str, lookbehind);\n                if (!match) {\n                  continue;\n                }\n              }\n              var from = match.index;\n              var matchStr = match[0];\n              var before = str.slice(0, from);\n              var after = str.slice(from + matchStr.length);\n              var reach = pos + str.length;\n              if (rematch && reach > rematch.reach) {\n                rematch.reach = reach;\n              }\n              var removeFrom = currentNode.prev;\n              if (before) {\n                removeFrom = addAfter(tokenList, removeFrom, before);\n                pos += before.length;\n              }\n              removeRange(tokenList, removeFrom, removeCount);\n              var wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n              currentNode = addAfter(tokenList, removeFrom, wrapped);\n              if (after) {\n                addAfter(tokenList, currentNode, after);\n              }\n              if (removeCount > 1) {\n                var nestedRematch = {\n                  cause: token + \",\" + j,\n                  reach\n                };\n                matchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n                if (rematch && nestedRematch.reach > rematch.reach) {\n                  rematch.reach = nestedRematch.reach;\n                }\n              }\n            }\n          }\n        }\n      }\n      function LinkedList() {\n        var head = { value: null, prev: null, next: null };\n        var tail = { value: null, prev: head, next: null };\n        head.next = tail;\n        this.head = head;\n        this.tail = tail;\n        this.length = 0;\n      }\n      function addAfter(list, node, value) {\n        var next = node.next;\n        var newNode = { value, prev: node, next };\n        node.next = newNode;\n        next.prev = newNode;\n        list.length++;\n        return newNode;\n      }\n      function removeRange(list, node, count) {\n        var next = node.next;\n        for (var i = 0; i < count && next !== list.tail; i++) {\n          next = next.next;\n        }\n        node.next = next;\n        next.prev = node;\n        list.length -= i;\n      }\n      function toArray(list) {\n        var array = [];\n        var node = list.head.next;\n        while (node !== list.tail) {\n          array.push(node.value);\n          node = node.next;\n        }\n        return array;\n      }\n      return _;\n    }();\n    module.exports = Prism2;\n    Prism2.default = Prism2;\n  }\n});\n\n// src/prism-langs.ts\nvar Prism = __toESM(require_prism());\nPrism.languages.markup = { comment: { pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/, greedy: true }, prolog: { pattern: /<\\?[\\s\\S]+?\\?>/, greedy: true }, doctype: { pattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i, greedy: true, inside: { \"internal-subset\": { pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/, lookbehind: true, greedy: true, inside: null }, string: { pattern: /\"[^\"]*\"|'[^']*'/, greedy: true }, punctuation: /^<!|>$|[[\\]]/, \"doctype-tag\": /^DOCTYPE/i, name: /[^\\s<>'\"]+/ } }, cdata: { pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i, greedy: true }, tag: { pattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/, greedy: true, inside: { tag: { pattern: /^<\\/?[^\\s>\\/]+/, inside: { punctuation: /^<\\/?/, namespace: /^[^\\s>\\/:]+:/ } }, \"special-attr\": [], \"attr-value\": { pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/, inside: { punctuation: [{ pattern: /^=/, alias: \"attr-equals\" }, { pattern: /^(\\s*)[\"']|[\"']$/, lookbehind: true }] } }, punctuation: /\\/?>/, \"attr-name\": { pattern: /[^\\s>\\/]+/, inside: { namespace: /^[^\\s>\\/:]+:/ } } } }, entity: [{ pattern: /&[\\da-z]{1,8};/i, alias: \"named-entity\" }, /&#x?[\\da-f]{1,8};/i] }, Prism.languages.markup.tag.inside[\"attr-value\"].inside.entity = Prism.languages.markup.entity, Prism.languages.markup.doctype.inside[\"internal-subset\"].inside = Prism.languages.markup, Prism.hooks.add(\"wrap\", function(e) {\n  \"entity\" === e.type && (e.attributes.title = e.content.replace(/&amp;/, \"&\"));\n}), Object.defineProperty(Prism.languages.markup.tag, \"addInlined\", { value: function(e, n) {\n  var t = {}, t = (t[\"language-\" + n] = { pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i, lookbehind: true, inside: Prism.languages[n] }, t.cdata = /^<!\\[CDATA\\[|\\]\\]>$/i, { \"included-cdata\": { pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i, inside: t } }), n = (t[\"language-\" + n] = { pattern: /[\\s\\S]+/, inside: Prism.languages[n] }, {});\n  n[e] = { pattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function() {\n    return e;\n  }), \"i\"), lookbehind: true, greedy: true, inside: t }, Prism.languages.insertBefore(\"markup\", \"cdata\", n);\n} }), Object.defineProperty(Prism.languages.markup.tag, \"addAttribute\", { value: function(e, n) {\n  Prism.languages.markup.tag.inside[\"special-attr\"].push({ pattern: RegExp(/(^|[\"'\\s])/.source + \"(?:\" + e + \")\" + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source, \"i\"), lookbehind: true, inside: { \"attr-name\": /^[^\\s=]+/, \"attr-value\": { pattern: /=[\\s\\S]+/, inside: { value: { pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/, lookbehind: true, alias: [n, \"language-\" + n], inside: Prism.languages[n] }, punctuation: [{ pattern: /^=/, alias: \"attr-equals\" }, /\"|'/] } } } });\n} }), Prism.languages.html = Prism.languages.markup, Prism.languages.mathml = Prism.languages.markup, Prism.languages.svg = Prism.languages.markup, Prism.languages.xml = Prism.languages.extend(\"markup\", {}), Prism.languages.ssml = Prism.languages.xml, Prism.languages.atom = Prism.languages.xml, Prism.languages.rss = Prism.languages.xml, function(e) {\n  var n = { pattern: /\\\\[\\\\(){}[\\]^$+*?|.]/, alias: \"escape\" }, t = /\\\\(?:x[\\da-fA-F]{2}|u[\\da-fA-F]{4}|u\\{[\\da-fA-F]+\\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/, a = \"(?:[^\\\\\\\\-]|\" + t.source + \")\", a = RegExp(a + \"-\" + a), r = { pattern: /(<|')[^<>']+(?=[>']$)/, lookbehind: true, alias: \"variable\" };\n  e.languages.regex = { \"char-class\": { pattern: /((?:^|[^\\\\])(?:\\\\\\\\)*)\\[(?:[^\\\\\\]]|\\\\[\\s\\S])*\\]/, lookbehind: true, inside: { \"char-class-negation\": { pattern: /(^\\[)\\^/, lookbehind: true, alias: \"operator\" }, \"char-class-punctuation\": { pattern: /^\\[|\\]$/, alias: \"punctuation\" }, range: { pattern: a, inside: { escape: t, \"range-punctuation\": { pattern: /-/, alias: \"operator\" } } }, \"special-escape\": n, \"char-set\": { pattern: /\\\\[wsd]|\\\\p\\{[^{}]+\\}/i, alias: \"class-name\" }, escape: t } }, \"special-escape\": n, \"char-set\": { pattern: /\\.|\\\\[wsd]|\\\\p\\{[^{}]+\\}/i, alias: \"class-name\" }, backreference: [{ pattern: /\\\\(?![123][0-7]{2})[1-9]/, alias: \"keyword\" }, { pattern: /\\\\k<[^<>']+>/, alias: \"keyword\", inside: { \"group-name\": r } }], anchor: { pattern: /[$^]|\\\\[ABbGZz]/, alias: \"function\" }, escape: t, group: [{ pattern: /\\((?:\\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/, alias: \"punctuation\", inside: { \"group-name\": r } }, { pattern: /\\)/, alias: \"punctuation\" }], quantifier: { pattern: /(?:[+*?]|\\{\\d+(?:,\\d*)?\\})[?+]?/, alias: \"number\" }, alternation: { pattern: /\\|/, alias: \"keyword\" } };\n}(Prism), Prism.languages.clike = { comment: [{ pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/, lookbehind: true, greedy: true }, { pattern: /(^|[^\\\\:])\\/\\/.*/, lookbehind: true, greedy: true }], string: { pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/, greedy: true }, \"class-name\": { pattern: /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i, lookbehind: true, inside: { punctuation: /[.\\\\]/ } }, keyword: /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/, boolean: /\\b(?:false|true)\\b/, function: /\\b\\w+(?=\\()/, number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i, operator: /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/, punctuation: /[{}[\\];(),.:]/ }, Prism.languages.javascript = Prism.languages.extend(\"clike\", { \"class-name\": [Prism.languages.clike[\"class-name\"], { pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/, lookbehind: true }], keyword: [{ pattern: /((?:^|\\})\\s*)catch\\b/, lookbehind: true }, { pattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/, lookbehind: true }], function: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/, number: { pattern: RegExp(/(^|[^\\w$])/.source + \"(?:\" + /NaN|Infinity/.source + \"|\" + /0[bB][01]+(?:_[01]+)*n?/.source + \"|\" + /0[oO][0-7]+(?:_[0-7]+)*n?/.source + \"|\" + /0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source + \"|\" + /\\d+(?:_\\d+)*n/.source + \"|\" + /(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source + \")\" + /(?![\\w$])/.source), lookbehind: true }, operator: /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/ }), Prism.languages.javascript[\"class-name\"][0].pattern = /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/, Prism.languages.insertBefore(\"javascript\", \"keyword\", { regex: { pattern: RegExp(/((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)/.source + /\\//.source + \"(?:\" + /(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}/.source + \"|\" + /(?:\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.)*\\])*\\])*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source + \")\" + /(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/.source), lookbehind: true, greedy: true, inside: { \"regex-source\": { pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/, lookbehind: true, alias: \"language-regex\", inside: Prism.languages.regex }, \"regex-delimiter\": /^\\/|\\/$/, \"regex-flags\": /^[a-z]+$/ } }, \"function-variable\": { pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/, alias: \"function\" }, parameter: [{ pattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/, lookbehind: true, inside: Prism.languages.javascript }, { pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i, lookbehind: true, inside: Prism.languages.javascript }, { pattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/, lookbehind: true, inside: Prism.languages.javascript }, { pattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/, lookbehind: true, inside: Prism.languages.javascript }], constant: /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/ }), Prism.languages.insertBefore(\"javascript\", \"string\", { hashbang: { pattern: /^#!.*/, greedy: true, alias: \"comment\" }, \"template-string\": { pattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/, greedy: true, inside: { \"template-punctuation\": { pattern: /^`|`$/, alias: \"string\" }, interpolation: { pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/, lookbehind: true, inside: { \"interpolation-punctuation\": { pattern: /^\\$\\{|\\}$/, alias: \"punctuation\" }, rest: Prism.languages.javascript } }, string: /[\\s\\S]+/ } }, \"string-property\": { pattern: /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m, lookbehind: true, greedy: true, alias: \"property\" } }), Prism.languages.insertBefore(\"javascript\", \"operator\", { \"literal-property\": { pattern: /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m, lookbehind: true, alias: \"property\" } }), Prism.languages.markup && (Prism.languages.markup.tag.addInlined(\"script\", \"javascript\"), Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source, \"javascript\")), Prism.languages.js = Prism.languages.javascript, Prism.languages.actionscript = Prism.languages.extend(\"javascript\", { keyword: /\\b(?:as|break|case|catch|class|const|default|delete|do|dynamic|each|else|extends|final|finally|for|function|get|if|implements|import|in|include|instanceof|interface|internal|is|namespace|native|new|null|override|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|use|var|void|while|with)\\b/, operator: /\\+\\+|--|(?:[+\\-*\\/%^]|&&?|\\|\\|?|<<?|>>?>?|[!=]=?)=?|[~?@]/ }), Prism.languages.actionscript[\"class-name\"].alias = \"function\", delete Prism.languages.actionscript.parameter, delete Prism.languages.actionscript[\"literal-property\"], Prism.languages.markup && Prism.languages.insertBefore(\"actionscript\", \"string\", { xml: { pattern: /(^|[^.])<\\/?\\w+(?:\\s+[^\\s>\\/=]+=(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\])*\\2)*\\s*\\/?>/, lookbehind: true, inside: Prism.languages.markup } }), function(e) {\n  var n = /#(?!\\{).+/, t = { pattern: /#\\{[^}]+\\}/, alias: \"variable\" };\n  e.languages.coffeescript = e.languages.extend(\"javascript\", { comment: n, string: [{ pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/, greedy: true }, { pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/, greedy: true, inside: { interpolation: t } }], keyword: /\\b(?:and|break|by|catch|class|continue|debugger|delete|do|each|else|extend|extends|false|finally|for|if|in|instanceof|is|isnt|let|loop|namespace|new|no|not|null|of|off|on|or|own|return|super|switch|then|this|throw|true|try|typeof|undefined|unless|until|when|while|window|with|yes|yield)\\b/, \"class-member\": { pattern: /@(?!\\d)\\w+/, alias: \"variable\" } }), e.languages.insertBefore(\"coffeescript\", \"comment\", { \"multiline-comment\": { pattern: /###[\\s\\S]+?###/, alias: \"comment\" }, \"block-regex\": { pattern: /\\/{3}[\\s\\S]*?\\/{3}/, alias: \"regex\", inside: { comment: n, interpolation: t } } }), e.languages.insertBefore(\"coffeescript\", \"string\", { \"inline-javascript\": { pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/, inside: { delimiter: { pattern: /^`|`$/, alias: \"punctuation\" }, script: { pattern: /[\\s\\S]+/, alias: \"language-javascript\", inside: e.languages.javascript } } }, \"multiline-string\": [{ pattern: /'''[\\s\\S]*?'''/, greedy: true, alias: \"string\" }, { pattern: /\"\"\"[\\s\\S]*?\"\"\"/, greedy: true, alias: \"string\", inside: { interpolation: t } }] }), e.languages.insertBefore(\"coffeescript\", \"keyword\", { property: /(?!\\d)\\w+(?=\\s*:(?!:))/ }), delete e.languages.coffeescript[\"template-string\"], e.languages.coffee = e.languages.coffeescript;\n}(Prism), function(l) {\n  var e = l.languages.javadoclike = { parameter: { pattern: /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*@(?:arg|arguments|param)\\s+)\\w+/m, lookbehind: true }, keyword: { pattern: /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*|\\{)@[a-z][a-zA-Z-]+\\b/m, lookbehind: true }, punctuation: /[{}]/ };\n  Object.defineProperty(e, \"addSupport\", { value: function(e2, o) {\n    (e2 = \"string\" == typeof e2 ? [e2] : e2).forEach(function(e3) {\n      var n = function(e4) {\n        e4.inside || (e4.inside = {}), e4.inside.rest = o;\n      }, t = \"doc-comment\";\n      if (a = l.languages[e3]) {\n        var a, r = a[t];\n        if ((r = r ? r : (a = l.languages.insertBefore(e3, \"comment\", { \"doc-comment\": { pattern: /(^|[^\\\\])\\/\\*\\*[^/][\\s\\S]*?(?:\\*\\/|$)/, lookbehind: true, alias: \"comment\" } }))[t]) instanceof RegExp && (r = a[t] = { pattern: r }), Array.isArray(r))\n          for (var s = 0, i = r.length; s < i; s++)\n            r[s] instanceof RegExp && (r[s] = { pattern: r[s] }), n(r[s]);\n        else\n          n(r);\n      }\n    });\n  } }), e.addSupport([\"java\", \"javascript\", \"php\"], e);\n}(Prism), function(e) {\n  var n = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/, n = (e.languages.css = { comment: /\\/\\*[\\s\\S]*?\\*\\//, atrule: { pattern: RegExp(\"@[\\\\w-](?:\" + /[^;{\\s\"']|\\s+(?!\\s)/.source + \"|\" + n.source + \")*?\" + /(?:;|(?=\\s*\\{))/.source), inside: { rule: /^@[\\w-]+/, \"selector-function-argument\": { pattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/, lookbehind: true, alias: \"selector\" }, keyword: { pattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/, lookbehind: true } } }, url: { pattern: RegExp(\"\\\\burl\\\\((?:\" + n.source + \"|\" + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + \")\\\\)\", \"i\"), greedy: true, inside: { function: /^url/i, punctuation: /^\\(|\\)$/, string: { pattern: RegExp(\"^\" + n.source + \"$\"), alias: \"url\" } } }, selector: { pattern: RegExp(`(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"'\\\\s]|\\\\s+(?![\\\\s{])|` + n.source + \")*(?=\\\\s*\\\\{)\"), lookbehind: true }, string: { pattern: n, greedy: true }, property: { pattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i, lookbehind: true }, important: /!important\\b/i, function: { pattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i, lookbehind: true }, punctuation: /[(){};:,]/ }, e.languages.css.atrule.inside.rest = e.languages.css, e.languages.markup);\n  n && (n.tag.addInlined(\"style\", \"css\"), n.tag.addAttribute(\"style\", \"css\"));\n}(Prism), function(e) {\n  var n = /(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/, n = (e.languages.css.selector = { pattern: e.languages.css.selector.pattern, lookbehind: true, inside: n = { \"pseudo-element\": /:(?:after|before|first-letter|first-line|selection)|::[-\\w]+/, \"pseudo-class\": /:[-\\w]+/, class: /\\.[-\\w]+/, id: /#[-\\w]+/, attribute: { pattern: RegExp(`\\\\[(?:[^[\\\\]\"']|` + n.source + \")*\\\\]\"), greedy: true, inside: { punctuation: /^\\[|\\]$/, \"case-sensitivity\": { pattern: /(\\s)[si]$/i, lookbehind: true, alias: \"keyword\" }, namespace: { pattern: /^(\\s*)(?:(?!\\s)[-*\\w\\xA0-\\uFFFF])*\\|(?!=)/, lookbehind: true, inside: { punctuation: /\\|$/ } }, \"attr-name\": { pattern: /^(\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+/, lookbehind: true }, \"attr-value\": [n, { pattern: /(=\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+(?=\\s*$)/, lookbehind: true }], operator: /[|~*^$]?=/ } }, \"n-th\": [{ pattern: /(\\(\\s*)[+-]?\\d*[\\dn](?:\\s*[+-]\\s*\\d+)?(?=\\s*\\))/, lookbehind: true, inside: { number: /[\\dn]+/, operator: /[+-]/ } }, { pattern: /(\\(\\s*)(?:even|odd)(?=\\s*\\))/i, lookbehind: true }], combinator: />|\\+|~|\\|\\|/, punctuation: /[(),]/ } }, e.languages.css.atrule.inside[\"selector-function-argument\"].inside = n, e.languages.insertBefore(\"css\", \"property\", { variable: { pattern: /(^|[^-\\w\\xA0-\\uFFFF])--(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*/i, lookbehind: true } }), { pattern: /(\\b\\d+)(?:%|[a-z]+(?![\\w-]))/, lookbehind: true }), t = { pattern: /(^|[^\\w.-])-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/, lookbehind: true };\n  e.languages.insertBefore(\"css\", \"function\", { operator: { pattern: /(\\s)[+\\-*\\/](?=\\s)/, lookbehind: true }, hexcode: { pattern: /\\B#[\\da-f]{3,8}\\b/i, alias: \"color\" }, color: [{ pattern: /(^|[^\\w-])(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|RebeccaPurple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)(?![\\w-])/i, lookbehind: true }, { pattern: /\\b(?:hsl|rgb)\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*\\)\\B|\\b(?:hsl|rgb)a\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*,\\s*(?:0|0?\\.\\d+|1)\\s*\\)\\B/i, inside: { unit: n, number: t, function: /[\\w-]+(?=\\()/, punctuation: /[(),]/ } }], entity: /\\\\[\\da-f]{1,8}/i, unit: n, number: t });\n}(Prism), function(e) {\n  var n = /[*&][^\\s[\\]{},]+/, t = /!(?:<[\\w\\-%#;/?:@&=+$,.!~*'()[\\]]+>|(?:[a-zA-Z\\d-]*!)?[\\w\\-%#;/?:@&=+$.~*'()]+)?/, a = \"(?:\" + t.source + \"(?:[ \t]+\" + n.source + \")?|\" + n.source + \"(?:[ \t]+\" + t.source + \")?)\", r = /(?:[^\\s\\x00-\\x08\\x0e-\\x1f!\"#%&'*,\\-:>?@[\\]`{|}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]|[?:-]<PLAIN>)(?:[ \\t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g, function() {\n    return /[^\\s\\x00-\\x08\\x0e-\\x1f,[\\]{}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]/.source;\n  }), s = /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/.source;\n  function i(e2, n2) {\n    n2 = (n2 || \"\").replace(/m/g, \"\") + \"m\";\n    var t2 = /([:\\-,[{]\\s*(?:\\s<<prop>>[ \\t]+)?)(?:<<value>>)(?=[ \\t]*(?:$|,|\\]|\\}|(?:[\\r\\n]\\s*)?#))/.source.replace(/<<prop>>/g, function() {\n      return a;\n    }).replace(/<<value>>/g, function() {\n      return e2;\n    });\n    return RegExp(t2, n2);\n  }\n  e.languages.yaml = { scalar: { pattern: RegExp(/([\\-:]\\s*(?:\\s<<prop>>[ \\t]+)?[|>])[ \\t]*(?:((?:\\r?\\n|\\r)[ \\t]+)\\S[^\\r\\n]*(?:\\2[^\\r\\n]+)*)/.source.replace(/<<prop>>/g, function() {\n    return a;\n  })), lookbehind: true, alias: \"string\" }, comment: /#.*/, key: { pattern: RegExp(/((?:^|[:\\-,[{\\r\\n?])[ \\t]*(?:<<prop>>[ \\t]+)?)<<key>>(?=\\s*:\\s)/.source.replace(/<<prop>>/g, function() {\n    return a;\n  }).replace(/<<key>>/g, function() {\n    return \"(?:\" + r + \"|\" + s + \")\";\n  })), lookbehind: true, greedy: true, alias: \"atrule\" }, directive: { pattern: /(^[ \\t]*)%.+/m, lookbehind: true, alias: \"important\" }, datetime: { pattern: i(/\\d{4}-\\d\\d?-\\d\\d?(?:[tT]|[ \\t]+)\\d\\d?:\\d{2}:\\d{2}(?:\\.\\d*)?(?:[ \\t]*(?:Z|[-+]\\d\\d?(?::\\d{2})?))?|\\d{4}-\\d{2}-\\d{2}|\\d\\d?:\\d{2}(?::\\d{2}(?:\\.\\d*)?)?/.source), lookbehind: true, alias: \"number\" }, boolean: { pattern: i(/false|true/.source, \"i\"), lookbehind: true, alias: \"important\" }, null: { pattern: i(/null|~/.source, \"i\"), lookbehind: true, alias: \"important\" }, string: { pattern: i(s), lookbehind: true, greedy: true }, number: { pattern: i(/[+-]?(?:0x[\\da-f]+|0o[0-7]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|\\.inf|\\.nan)/.source, \"i\"), lookbehind: true }, tag: t, important: n, punctuation: /---|[:[\\]{}\\-,|>?]|\\.\\.\\./ }, e.languages.yml = e.languages.yaml;\n}(Prism), function(o) {\n  var n = /(?:\\\\.|[^\\\\\\n\\r]|(?:\\n|\\r\\n?)(?![\\r\\n]))/.source;\n  function e(e2) {\n    return e2 = e2.replace(/<inner>/g, function() {\n      return n;\n    }), RegExp(/((?:^|[^\\\\])(?:\\\\{2})*)/.source + \"(?:\" + e2 + \")\");\n  }\n  var t = /(?:\\\\.|``(?:[^`\\r\\n]|`(?!`))+``|`[^`\\r\\n]+`|[^\\\\|\\r\\n`])+/.source, a = /\\|?__(?:\\|__)+\\|?(?:(?:\\n|\\r\\n?)|(?![\\s\\S]))/.source.replace(/__/g, function() {\n    return t;\n  }), r = /\\|?[ \\t]*:?-{3,}:?[ \\t]*(?:\\|[ \\t]*:?-{3,}:?[ \\t]*)+\\|?(?:\\n|\\r\\n?)/.source, l = (o.languages.markdown = o.languages.extend(\"markup\", {}), o.languages.insertBefore(\"markdown\", \"prolog\", { \"front-matter-block\": { pattern: /(^(?:\\s*[\\r\\n])?)---(?!.)[\\s\\S]*?[\\r\\n]---(?!.)/, lookbehind: true, greedy: true, inside: { punctuation: /^---|---$/, \"front-matter\": { pattern: /\\S+(?:\\s+\\S+)*/, alias: [\"yaml\", \"language-yaml\"], inside: o.languages.yaml } } }, blockquote: { pattern: /^>(?:[\\t ]*>)*/m, alias: \"punctuation\" }, table: { pattern: RegExp(\"^\" + a + r + \"(?:\" + a + \")*\", \"m\"), inside: { \"table-data-rows\": { pattern: RegExp(\"^(\" + a + r + \")(?:\" + a + \")*$\"), lookbehind: true, inside: { \"table-data\": { pattern: RegExp(t), inside: o.languages.markdown }, punctuation: /\\|/ } }, \"table-line\": { pattern: RegExp(\"^(\" + a + \")\" + r + \"$\"), lookbehind: true, inside: { punctuation: /\\||:?-{3,}:?/ } }, \"table-header-row\": { pattern: RegExp(\"^\" + a + \"$\"), inside: { \"table-header\": { pattern: RegExp(t), alias: \"important\", inside: o.languages.markdown }, punctuation: /\\|/ } } } }, code: [{ pattern: /((?:^|\\n)[ \\t]*\\n|(?:^|\\r\\n?)[ \\t]*\\r\\n?)(?: {4}|\\t).+(?:(?:\\n|\\r\\n?)(?: {4}|\\t).+)*/, lookbehind: true, alias: \"keyword\" }, { pattern: /^```[\\s\\S]*?^```$/m, greedy: true, inside: { \"code-block\": { pattern: /^(```.*(?:\\n|\\r\\n?))[\\s\\S]+?(?=(?:\\n|\\r\\n?)^```$)/m, lookbehind: true }, \"code-language\": { pattern: /^(```).+/, lookbehind: true }, punctuation: /```/ } }], title: [{ pattern: /\\S.*(?:\\n|\\r\\n?)(?:==+|--+)(?=[ \\t]*$)/m, alias: \"important\", inside: { punctuation: /==+$|--+$/ } }, { pattern: /(^\\s*)#.+/m, lookbehind: true, alias: \"important\", inside: { punctuation: /^#+|#+$/ } }], hr: { pattern: /(^\\s*)([*-])(?:[\\t ]*\\2){2,}(?=\\s*$)/m, lookbehind: true, alias: \"punctuation\" }, list: { pattern: /(^\\s*)(?:[*+-]|\\d+\\.)(?=[\\t ].)/m, lookbehind: true, alias: \"punctuation\" }, \"url-reference\": { pattern: /!?\\[[^\\]]+\\]:[\\t ]+(?:\\S+|<(?:\\\\.|[^>\\\\])+>)(?:[\\t ]+(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\)))?/, inside: { variable: { pattern: /^(!?\\[)[^\\]]+/, lookbehind: true }, string: /(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\))$/, punctuation: /^[\\[\\]!:]|[<>]/ }, alias: \"url\" }, bold: { pattern: e(/\\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\\b|\\*\\*(?:(?!\\*)<inner>|\\*(?:(?!\\*)<inner>)+\\*)+\\*\\*/.source), lookbehind: true, greedy: true, inside: { content: { pattern: /(^..)[\\s\\S]+(?=..$)/, lookbehind: true, inside: {} }, punctuation: /\\*\\*|__/ } }, italic: { pattern: e(/\\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\\b|\\*(?:(?!\\*)<inner>|\\*\\*(?:(?!\\*)<inner>)+\\*\\*)+\\*/.source), lookbehind: true, greedy: true, inside: { content: { pattern: /(^.)[\\s\\S]+(?=.$)/, lookbehind: true, inside: {} }, punctuation: /[*_]/ } }, strike: { pattern: e(/(~~?)(?:(?!~)<inner>)+\\2/.source), lookbehind: true, greedy: true, inside: { content: { pattern: /(^~~?)[\\s\\S]+(?=\\1$)/, lookbehind: true, inside: {} }, punctuation: /~~?/ } }, \"code-snippet\": { pattern: /(^|[^\\\\`])(?:``[^`\\r\\n]+(?:`[^`\\r\\n]+)*``(?!`)|`[^`\\r\\n]+`(?!`))/, lookbehind: true, greedy: true, alias: [\"code\", \"keyword\"] }, url: { pattern: e(/!?\\[(?:(?!\\])<inner>)+\\](?:\\([^\\s)]+(?:[\\t ]+\"(?:\\\\.|[^\"\\\\])*\")?\\)|[ \\t]?\\[(?:(?!\\])<inner>)+\\])/.source), lookbehind: true, greedy: true, inside: { operator: /^!/, content: { pattern: /(^\\[)[^\\]]+(?=\\])/, lookbehind: true, inside: {} }, variable: { pattern: /(^\\][ \\t]?\\[)[^\\]]+(?=\\]$)/, lookbehind: true }, url: { pattern: /(^\\]\\()[^\\s)]+/, lookbehind: true }, string: { pattern: /(^[ \\t]+)\"(?:\\\\.|[^\"\\\\])*\"(?=\\)$)/, lookbehind: true } } } }), [\"url\", \"bold\", \"italic\", \"strike\"].forEach(function(n2) {\n    [\"url\", \"bold\", \"italic\", \"strike\", \"code-snippet\"].forEach(function(e2) {\n      n2 !== e2 && (o.languages.markdown[n2].inside.content.inside[e2] = o.languages.markdown[e2]);\n    });\n  }), o.hooks.add(\"after-tokenize\", function(e2) {\n    \"markdown\" !== e2.language && \"md\" !== e2.language || !function e3(n2) {\n      if (n2 && \"string\" != typeof n2)\n        for (var t2 = 0, a2 = n2.length; t2 < a2; t2++) {\n          var r2, s = n2[t2];\n          \"code\" !== s.type ? e3(s.content) : (r2 = s.content[1], s = s.content[3], r2 && s && \"code-language\" === r2.type && \"code-block\" === s.type && \"string\" == typeof r2.content && (r2 = r2.content.replace(/\\b#/g, \"sharp\").replace(/\\b\\+\\+/g, \"pp\"), r2 = \"language-\" + (r2 = (/[a-z][\\w-]*/i.exec(r2) || [\"\"])[0].toLowerCase()), s.alias ? \"string\" == typeof s.alias ? s.alias = [s.alias, r2] : s.alias.push(r2) : s.alias = [r2]));\n        }\n    }(e2.tokens);\n  }), o.hooks.add(\"wrap\", function(e2) {\n    if (\"code-block\" === e2.type) {\n      for (var n2 = \"\", t2 = 0, a2 = e2.classes.length; t2 < a2; t2++) {\n        var r2 = e2.classes[t2], r2 = /language-(.+)/.exec(r2);\n        if (r2) {\n          n2 = r2[1];\n          break;\n        }\n      }\n      var s, i = o.languages[n2];\n      i ? e2.content = o.highlight(function(e3) {\n        e3 = e3.replace(l, \"\");\n        return e3 = e3.replace(/&(\\w{1,8}|#x?[\\da-f]{1,8});/gi, function(e4, n3) {\n          var t3;\n          return \"#\" === (n3 = n3.toLowerCase())[0] ? (t3 = \"x\" === n3[1] ? parseInt(n3.slice(2), 16) : Number(n3.slice(1)), c(t3)) : u[n3] || e4;\n        });\n      }(e2.content), i, n2) : n2 && \"none\" !== n2 && o.plugins.autoloader && (s = \"md-\" + (/* @__PURE__ */ new Date()).valueOf() + \"-\" + Math.floor(1e16 * Math.random()), e2.attributes.id = s, o.plugins.autoloader.loadLanguages(n2, function() {\n        var e3 = document.getElementById(s);\n        e3 && (e3.innerHTML = o.highlight(e3.textContent, o.languages[n2], n2));\n      }));\n    }\n  }), RegExp(o.languages.markup.tag.pattern.source, \"gi\")), u = { amp: \"&\", lt: \"<\", gt: \">\", quot: '\"' }, c = String.fromCodePoint || String.fromCharCode;\n  o.languages.md = o.languages.markdown;\n}(Prism), Prism.languages.graphql = { comment: /#.*/, description: { pattern: /(?:\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")(?=\\s*[a-z_])/i, greedy: true, alias: \"string\", inside: { \"language-markdown\": { pattern: /(^\"(?:\"\")?)(?!\\1)[\\s\\S]+(?=\\1$)/, lookbehind: true, inside: Prism.languages.markdown } } }, string: { pattern: /\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/, greedy: true }, number: /(?:\\B-|\\b)\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i, boolean: /\\b(?:false|true)\\b/, variable: /\\$[a-z_]\\w*/i, directive: { pattern: /@[a-z_]\\w*/i, alias: \"function\" }, \"attr-name\": { pattern: /\\b[a-z_]\\w*(?=\\s*(?:\\((?:[^()\"]|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")*\\))?:)/i, greedy: true }, \"atom-input\": { pattern: /\\b[A-Z]\\w*Input\\b/, alias: \"class-name\" }, scalar: /\\b(?:Boolean|Float|ID|Int|String)\\b/, constant: /\\b[A-Z][A-Z_\\d]*\\b/, \"class-name\": { pattern: /(\\b(?:enum|implements|interface|on|scalar|type|union)\\s+|&\\s*|:\\s*|\\[)[A-Z_]\\w*/, lookbehind: true }, fragment: { pattern: /(\\bfragment\\s+|\\.{3}\\s*(?!on\\b))[a-zA-Z_]\\w*/, lookbehind: true, alias: \"function\" }, \"definition-mutation\": { pattern: /(\\bmutation\\s+)[a-zA-Z_]\\w*/, lookbehind: true, alias: \"function\" }, \"definition-query\": { pattern: /(\\bquery\\s+)[a-zA-Z_]\\w*/, lookbehind: true, alias: \"function\" }, keyword: /\\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\\b/, operator: /[!=|&]|\\.{3}/, \"property-query\": /\\w+(?=\\s*\\()/, object: /\\w+(?=\\s*\\{)/, punctuation: /[!(){}\\[\\]:=,]/, property: /\\w+/ }, Prism.hooks.add(\"after-tokenize\", function(e) {\n  if (\"graphql\" === e.language)\n    for (var i = e.tokens.filter(function(e2) {\n      return \"string\" != typeof e2 && \"comment\" !== e2.type && \"scalar\" !== e2.type;\n    }), o = 0; o < i.length; ) {\n      var n = i[o++];\n      if (\"keyword\" === n.type && \"mutation\" === n.content) {\n        var t = [];\n        if (p([\"definition-mutation\", \"punctuation\"]) && \"(\" === c(1).content) {\n          o += 2;\n          var a = d(/^\\($/, /^\\)$/);\n          if (-1 === a)\n            continue;\n          for (; o < a; o++) {\n            var r = c(0);\n            \"variable\" === r.type && (g(r, \"variable-input\"), t.push(r.content));\n          }\n          o = a + 1;\n        }\n        if (p([\"punctuation\", \"property-query\"]) && \"{\" === c(0).content && (o++, g(c(0), \"property-mutation\"), 0 < t.length)) {\n          var s = d(/^\\{$/, /^\\}$/);\n          if (-1 !== s)\n            for (var l = o; l < s; l++) {\n              var u = i[l];\n              \"variable\" === u.type && 0 <= t.indexOf(u.content) && g(u, \"variable-input\");\n            }\n        }\n      }\n    }\n  function c(e2) {\n    return i[o + e2];\n  }\n  function p(e2, n2) {\n    n2 = n2 || 0;\n    for (var t2 = 0; t2 < e2.length; t2++) {\n      var a2 = c(t2 + n2);\n      if (!a2 || a2.type !== e2[t2])\n        return;\n    }\n    return 1;\n  }\n  function d(e2, n2) {\n    for (var t2 = 1, a2 = o; a2 < i.length; a2++) {\n      var r2 = i[a2], s2 = r2.content;\n      if (\"punctuation\" === r2.type && \"string\" == typeof s2) {\n        if (e2.test(s2))\n          t2++;\n        else if (n2.test(s2) && 0 === --t2)\n          return a2;\n      }\n    }\n    return -1;\n  }\n  function g(e2, n2) {\n    var t2 = e2.alias;\n    t2 ? Array.isArray(t2) || (e2.alias = t2 = [t2]) : e2.alias = t2 = [], t2.push(n2);\n  }\n}), Prism.languages.sql = { comment: { pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:--|\\/\\/|#).*)/, lookbehind: true }, variable: [{ pattern: /@([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1/, greedy: true }, /@[\\w.$]+/], string: { pattern: /(^|[^@\\\\])(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\]|\\2\\2)*\\2/, greedy: true, lookbehind: true }, identifier: { pattern: /(^|[^@\\\\])`(?:\\\\[\\s\\S]|[^`\\\\]|``)*`/, greedy: true, lookbehind: true, inside: { punctuation: /^`|`$/ } }, function: /\\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\\s*\\()/i, keyword: /\\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\\b/i, boolean: /\\b(?:FALSE|NULL|TRUE)\\b/i, number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+\\b/i, operator: /[-+*\\/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\\b/i, punctuation: /[;[\\]()`,.]/ }, function(b) {\n  var e = b.languages.javascript[\"template-string\"], t = e.pattern.source, m = e.inside.interpolation, f = m.inside[\"interpolation-punctuation\"], s = m.pattern.source;\n  function n(e2, n2) {\n    if (b.languages[e2])\n      return { pattern: RegExp(\"((?:\" + n2 + \")\\\\s*)\" + t), lookbehind: true, greedy: true, inside: { \"template-punctuation\": { pattern: /^`|`$/, alias: \"string\" }, \"embedded-code\": { pattern: /[\\s\\S]+/, alias: e2 } } };\n  }\n  function h(e2, n2, t2) {\n    e2 = { code: e2, grammar: n2, language: t2 };\n    return b.hooks.run(\"before-tokenize\", e2), e2.tokens = b.tokenize(e2.code, e2.grammar), b.hooks.run(\"after-tokenize\", e2), e2.tokens;\n  }\n  function l(a2, e2, r) {\n    var n2 = b.tokenize(a2, { interpolation: { pattern: RegExp(s), lookbehind: true } }), p = 0, d = {}, n2 = h(n2.map(function(e3) {\n      if (\"string\" == typeof e3)\n        return e3;\n      for (var n3, t2, e3 = e3.content; -1 !== a2.indexOf((t2 = p++, n3 = \"___\" + r.toUpperCase() + \"_\" + t2 + \"___\")); )\n        ;\n      return d[n3] = e3, n3;\n    }).join(\"\"), e2, r), g = Object.keys(d);\n    return p = 0, function e3(n3) {\n      for (var t2 = 0; t2 < n3.length; t2++) {\n        if (p >= g.length)\n          return;\n        var a3, r2, s2, i, o, l2, u2, c = n3[t2];\n        \"string\" == typeof c || \"string\" == typeof c.content ? (a3 = g[p], -1 !== (u2 = (l2 = \"string\" == typeof c ? c : c.content).indexOf(a3)) && (++p, r2 = l2.substring(0, u2), o = d[a3], s2 = void 0, (i = {})[\"interpolation-punctuation\"] = f, 3 === (i = b.tokenize(o, i)).length && ((s2 = [1, 1]).push.apply(s2, h(i[1], b.languages.javascript, \"javascript\")), i.splice.apply(i, s2)), s2 = new b.Token(\"interpolation\", i, m.alias, o), i = l2.substring(u2 + a3.length), o = [], r2 && o.push(r2), o.push(s2), i && (e3(l2 = [i]), o.push.apply(o, l2)), \"string\" == typeof c ? (n3.splice.apply(n3, [t2, 1].concat(o)), t2 += o.length - 1) : c.content = o)) : (u2 = c.content, Array.isArray(u2) ? e3(u2) : e3([u2]));\n      }\n    }(n2), new b.Token(r, n2, \"language-\" + r, a2);\n  }\n  b.languages.javascript[\"template-string\"] = [n(\"css\", /\\b(?:styled(?:\\([^)]*\\))?(?:\\s*\\.\\s*\\w+(?:\\([^)]*\\))*)*|css(?:\\s*\\.\\s*(?:global|resolve))?|createGlobalStyle|keyframes)/.source), n(\"html\", /\\bhtml|\\.\\s*(?:inner|outer)HTML\\s*\\+?=/.source), n(\"svg\", /\\bsvg/.source), n(\"markdown\", /\\b(?:markdown|md)/.source), n(\"graphql\", /\\b(?:gql|graphql(?:\\s*\\.\\s*experimental)?)/.source), n(\"sql\", /\\bsql/.source), e].filter(Boolean);\n  var a = { javascript: true, js: true, typescript: true, ts: true, jsx: true, tsx: true };\n  function u(e2) {\n    return \"string\" == typeof e2 ? e2 : Array.isArray(e2) ? e2.map(u).join(\"\") : u(e2.content);\n  }\n  b.hooks.add(\"after-tokenize\", function(e2) {\n    e2.language in a && !function e3(n2) {\n      for (var t2 = 0, a2 = n2.length; t2 < a2; t2++) {\n        var r, s2, i, o = n2[t2];\n        \"string\" != typeof o && (r = o.content, Array.isArray(r) ? \"template-string\" === o.type ? (o = r[1], 3 === r.length && \"string\" != typeof o && \"embedded-code\" === o.type && (s2 = u(o), o = o.alias, o = Array.isArray(o) ? o[0] : o, i = b.languages[o]) && (r[1] = l(s2, i, o))) : e3(r) : \"string\" != typeof r && e3([r]));\n      }\n    }(e2.tokens);\n  });\n}(Prism), function(e) {\n  e.languages.typescript = e.languages.extend(\"javascript\", { \"class-name\": { pattern: /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/, lookbehind: true, greedy: true, inside: null }, builtin: /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/ }), e.languages.typescript.keyword.push(/\\b(?:abstract|declare|is|keyof|readonly|require)\\b/, /\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/, /\\btype\\b(?=\\s*(?:[\\{*]|$))/), delete e.languages.typescript.parameter, delete e.languages.typescript[\"literal-property\"];\n  var n = e.languages.extend(\"typescript\", {});\n  delete n[\"class-name\"], e.languages.typescript[\"class-name\"].inside = n, e.languages.insertBefore(\"typescript\", \"function\", { decorator: { pattern: /@[$\\w\\xA0-\\uFFFF]+/, inside: { at: { pattern: /^@/, alias: \"operator\" }, function: /^[\\s\\S]+/ } }, \"generic-function\": { pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/, greedy: true, inside: { function: /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/, generic: { pattern: /<[\\s\\S]+/, alias: \"class-name\", inside: n } } } }), e.languages.ts = e.languages.typescript;\n}(Prism), function(e) {\n  var n = e.languages.javascript, t = /\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})+\\}/.source, a = \"(@(?:arg|argument|param|property)\\\\s+(?:\" + t + \"\\\\s+)?)\";\n  e.languages.jsdoc = e.languages.extend(\"javadoclike\", { parameter: { pattern: RegExp(a + /(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?=\\s|$)/.source), lookbehind: true, inside: { punctuation: /\\./ } } }), e.languages.insertBefore(\"jsdoc\", \"keyword\", { \"optional-parameter\": { pattern: RegExp(a + /\\[(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?:=[^[\\]]+)?\\](?=\\s|$)/.source), lookbehind: true, inside: { parameter: { pattern: /(^\\[)[$\\w\\xA0-\\uFFFF\\.]+/, lookbehind: true, inside: { punctuation: /\\./ } }, code: { pattern: /(=)[\\s\\S]*(?=\\]$)/, lookbehind: true, inside: n, alias: \"language-javascript\" }, punctuation: /[=[\\]]/ } }, \"class-name\": [{ pattern: RegExp(/(@(?:augments|class|extends|interface|memberof!?|template|this|typedef)\\s+(?:<TYPE>\\s+)?)[A-Z]\\w*(?:\\.[A-Z]\\w*)*/.source.replace(/<TYPE>/g, function() {\n    return t;\n  })), lookbehind: true, inside: { punctuation: /\\./ } }, { pattern: RegExp(\"(@[a-z]+\\\\s+)\" + t), lookbehind: true, inside: { string: n.string, number: n.number, boolean: n.boolean, keyword: e.languages.typescript.keyword, operator: /=>|\\.\\.\\.|[&|?:*]/, punctuation: /[.,;=<>{}()[\\]]/ } }], example: { pattern: /(@example\\s+(?!\\s))(?:[^@\\s]|\\s+(?!\\s))+?(?=\\s*(?:\\*\\s*)?(?:@\\w|\\*\\/))/, lookbehind: true, inside: { code: { pattern: /^([\\t ]*(?:\\*\\s*)?)\\S.*$/m, lookbehind: true, inside: n, alias: \"language-javascript\" } } } }), e.languages.javadoclike.addSupport(\"javascript\", e.languages.jsdoc);\n}(Prism), function(e) {\n  e.languages.flow = e.languages.extend(\"javascript\", {}), e.languages.insertBefore(\"flow\", \"keyword\", { type: [{ pattern: /\\b(?:[Bb]oolean|Function|[Nn]umber|[Ss]tring|[Ss]ymbol|any|mixed|null|void)\\b/, alias: \"class-name\" }] }), e.languages.flow[\"function-variable\"].pattern = /(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=\\s*(?:function\\b|(?:\\([^()]*\\)(?:\\s*:\\s*\\w+)?|(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/i, delete e.languages.flow.parameter, e.languages.insertBefore(\"flow\", \"operator\", { \"flow-punctuation\": { pattern: /\\{\\||\\|\\}/, alias: \"punctuation\" } }), Array.isArray(e.languages.flow.keyword) || (e.languages.flow.keyword = [e.languages.flow.keyword]), e.languages.flow.keyword.unshift({ pattern: /(^|[^$]\\b)(?:Class|declare|opaque|type)\\b(?!\\$)/, lookbehind: true }, { pattern: /(^|[^$]\\B)\\$(?:Diff|Enum|Exact|Keys|ObjMap|PropertyType|Record|Shape|Subtype|Supertype|await)\\b(?!\\$)/, lookbehind: true });\n}(Prism), Prism.languages.n4js = Prism.languages.extend(\"javascript\", { keyword: /\\b(?:Array|any|boolean|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|false|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|module|new|null|number|package|private|protected|public|return|set|static|string|super|switch|this|throw|true|try|typeof|var|void|while|with|yield)\\b/ }), Prism.languages.insertBefore(\"n4js\", \"constant\", { annotation: { pattern: /@+\\w+/, alias: \"operator\" } }), Prism.languages.n4jsd = Prism.languages.n4js, function(e) {\n  function n(e2, n2) {\n    return RegExp(e2.replace(/<ID>/g, function() {\n      return /(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/.source;\n    }), n2);\n  }\n  e.languages.insertBefore(\"javascript\", \"function-variable\", { \"method-variable\": { pattern: RegExp(\"(\\\\.\\\\s*)\" + e.languages.javascript[\"function-variable\"].pattern.source), lookbehind: true, alias: [\"function-variable\", \"method\", \"function\", \"property-access\"] } }), e.languages.insertBefore(\"javascript\", \"function\", { method: { pattern: RegExp(\"(\\\\.\\\\s*)\" + e.languages.javascript.function.source), lookbehind: true, alias: [\"function\", \"property-access\"] } }), e.languages.insertBefore(\"javascript\", \"constant\", { \"known-class-name\": [{ pattern: /\\b(?:(?:Float(?:32|64)|(?:Int|Uint)(?:8|16|32)|Uint8Clamped)?Array|ArrayBuffer|BigInt|Boolean|DataView|Date|Error|Function|Intl|JSON|(?:Weak)?(?:Map|Set)|Math|Number|Object|Promise|Proxy|Reflect|RegExp|String|Symbol|WebAssembly)\\b/, alias: \"class-name\" }, { pattern: /\\b(?:[A-Z]\\w*)Error\\b/, alias: \"class-name\" }] }), e.languages.insertBefore(\"javascript\", \"keyword\", { imports: { pattern: n(/(\\bimport\\b\\s*)(?:<ID>(?:\\s*,\\s*(?:\\*\\s*as\\s+<ID>|\\{[^{}]*\\}))?|\\*\\s*as\\s+<ID>|\\{[^{}]*\\})(?=\\s*\\bfrom\\b)/.source), lookbehind: true, inside: e.languages.javascript }, exports: { pattern: n(/(\\bexport\\b\\s*)(?:\\*(?:\\s*as\\s+<ID>)?(?=\\s*\\bfrom\\b)|\\{[^{}]*\\})/.source), lookbehind: true, inside: e.languages.javascript } }), e.languages.javascript.keyword.unshift({ pattern: /\\b(?:as|default|export|from|import)\\b/, alias: \"module\" }, { pattern: /\\b(?:await|break|catch|continue|do|else|finally|for|if|return|switch|throw|try|while|yield)\\b/, alias: \"control-flow\" }, { pattern: /\\bnull\\b/, alias: [\"null\", \"nil\"] }, { pattern: /\\bundefined\\b/, alias: \"nil\" }), e.languages.insertBefore(\"javascript\", \"operator\", { spread: { pattern: /\\.{3}/, alias: \"operator\" }, arrow: { pattern: /=>/, alias: \"operator\" } }), e.languages.insertBefore(\"javascript\", \"punctuation\", { \"property-access\": { pattern: n(/(\\.\\s*)#?<ID>/.source), lookbehind: true }, \"maybe-class-name\": { pattern: /(^|[^$\\w\\xA0-\\uFFFF])[A-Z][$\\w\\xA0-\\uFFFF]+/, lookbehind: true }, dom: { pattern: /\\b(?:document|(?:local|session)Storage|location|navigator|performance|window)\\b/, alias: \"variable\" }, console: { pattern: /\\bconsole(?=\\s*\\.)/, alias: \"class-name\" } });\n  for (var t = [\"function\", \"function-variable\", \"method\", \"method-variable\", \"property-access\"], a = 0; a < t.length; a++) {\n    var r = t[a], s = e.languages.javascript[r], r = (s = \"RegExp\" === e.util.type(s) ? e.languages.javascript[r] = { pattern: s } : s).inside || {};\n    (s.inside = r)[\"maybe-class-name\"] = /^[A-Z][\\s\\S]*/;\n  }\n}(Prism), function(s) {\n  var e = s.util.clone(s.languages.javascript), t = /(?:\\s|\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))\\*\\/)/.source, a = /(?:\\{(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])*\\})/.source, r = /(?:\\{<S>*\\.{3}(?:[^{}]|<BRACES>)*\\})/.source;\n  function n(e2, n2) {\n    return e2 = e2.replace(/<S>/g, function() {\n      return t;\n    }).replace(/<BRACES>/g, function() {\n      return a;\n    }).replace(/<SPREAD>/g, function() {\n      return r;\n    }), RegExp(e2, n2);\n  }\n  r = n(r).source, s.languages.jsx = s.languages.extend(\"markup\", e), s.languages.jsx.tag.pattern = n(/<\\/?(?:[\\w.:-]+(?:<S>+(?:[\\w.:$-]+(?:=(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s{'\"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\\/?)?>/.source), s.languages.jsx.tag.inside.tag.pattern = /^<\\/?[^\\s>\\/]*/, s.languages.jsx.tag.inside[\"attr-value\"].pattern = /=(?!\\{)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s'\">]+)/, s.languages.jsx.tag.inside.tag.inside[\"class-name\"] = /^[A-Z]\\w*(?:\\.[A-Z]\\w*)*$/, s.languages.jsx.tag.inside.comment = e.comment, s.languages.insertBefore(\"inside\", \"attr-name\", { spread: { pattern: n(/<SPREAD>/.source), inside: s.languages.jsx } }, s.languages.jsx.tag), s.languages.insertBefore(\"inside\", \"special-attr\", { script: { pattern: n(/=<BRACES>/.source), alias: \"language-javascript\", inside: { \"script-punctuation\": { pattern: /^=(?=\\{)/, alias: \"punctuation\" }, rest: s.languages.jsx } } }, s.languages.jsx.tag);\n  function i(e2) {\n    for (var n2 = [], t2 = 0; t2 < e2.length; t2++) {\n      var a2 = e2[t2], r2 = false;\n      \"string\" != typeof a2 && (\"tag\" === a2.type && a2.content[0] && \"tag\" === a2.content[0].type ? \"</\" === a2.content[0].content[0].content ? 0 < n2.length && n2[n2.length - 1].tagName === o(a2.content[0].content[1]) && n2.pop() : \"/>\" !== a2.content[a2.content.length - 1].content && n2.push({ tagName: o(a2.content[0].content[1]), openedBraces: 0 }) : 0 < n2.length && \"punctuation\" === a2.type && \"{\" === a2.content ? n2[n2.length - 1].openedBraces++ : 0 < n2.length && 0 < n2[n2.length - 1].openedBraces && \"punctuation\" === a2.type && \"}\" === a2.content ? n2[n2.length - 1].openedBraces-- : r2 = true), (r2 || \"string\" == typeof a2) && 0 < n2.length && 0 === n2[n2.length - 1].openedBraces && (r2 = o(a2), t2 < e2.length - 1 && (\"string\" == typeof e2[t2 + 1] || \"plain-text\" === e2[t2 + 1].type) && (r2 += o(e2[t2 + 1]), e2.splice(t2 + 1, 1)), 0 < t2 && (\"string\" == typeof e2[t2 - 1] || \"plain-text\" === e2[t2 - 1].type) && (r2 = o(e2[t2 - 1]) + r2, e2.splice(t2 - 1, 1), t2--), e2[t2] = new s.Token(\"plain-text\", r2, null, r2)), a2.content && \"string\" != typeof a2.content && i(a2.content);\n    }\n  }\n  var o = function(e2) {\n    return e2 ? \"string\" == typeof e2 ? e2 : \"string\" == typeof e2.content ? e2.content : e2.content.map(o).join(\"\") : \"\";\n  };\n  s.hooks.add(\"after-tokenize\", function(e2) {\n    \"jsx\" !== e2.language && \"tsx\" !== e2.language || i(e2.tokens);\n  });\n}(Prism), function(e) {\n  var n = e.util.clone(e.languages.typescript), n = (e.languages.tsx = e.languages.extend(\"jsx\", n), delete e.languages.tsx.parameter, delete e.languages.tsx[\"literal-property\"], e.languages.tsx.tag);\n  n.pattern = RegExp(/(^|[^\\w$]|(?=<\\/))/.source + \"(?:\" + n.pattern.source + \")\", n.pattern.flags), n.lookbehind = true;\n}(Prism), Prism.languages.swift = { comment: { pattern: /(^|[^\\\\:])(?:\\/\\/.*|\\/\\*(?:[^/*]|\\/(?!\\*)|\\*(?!\\/)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*\\*\\/)/, lookbehind: true, greedy: true }, \"string-literal\": [{ pattern: RegExp(/(^|[^\"#])/.source + \"(?:\" + /\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^(])|[^\\\\\\r\\n\"])*\"/.source + \"|\" + /\"\"\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|[^(])|[^\\\\\"]|\"(?!\"\"))*\"\"\"/.source + \")\" + /(?![\"#])/.source), lookbehind: true, greedy: true, inside: { interpolation: { pattern: /(\\\\\\()(?:[^()]|\\([^()]*\\))*(?=\\))/, lookbehind: true, inside: null }, \"interpolation-punctuation\": { pattern: /^\\)|\\\\\\($/, alias: \"punctuation\" }, punctuation: /\\\\(?=[\\r\\n])/, string: /[\\s\\S]+/ } }, { pattern: RegExp(/(^|[^\"#])(#+)/.source + \"(?:\" + /\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^#])|[^\\\\\\r\\n])*?\"/.source + \"|\" + /\"\"\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|[^#])|[^\\\\])*?\"\"\"/.source + \")\\\\2\"), lookbehind: true, greedy: true, inside: { interpolation: { pattern: /(\\\\#+\\()(?:[^()]|\\([^()]*\\))*(?=\\))/, lookbehind: true, inside: null }, \"interpolation-punctuation\": { pattern: /^\\)|\\\\#+\\($/, alias: \"punctuation\" }, string: /[\\s\\S]+/ } }], directive: { pattern: RegExp(/#/.source + \"(?:\" + /(?:elseif|if)\\b/.source + \"(?:[ \t]*\" + /(?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*\\((?:[^()]|\\([^()]*\\))*\\))?|\\((?:[^()]|\\([^()]*\\))*\\))(?:[ \\t]*(?:&&|\\|\\|))?/.source + \")+|\" + /(?:else|endif)\\b/.source + \")\"), alias: \"property\", inside: { \"directive-name\": /^#\\w+/, boolean: /\\b(?:false|true)\\b/, number: /\\b\\d+(?:\\.\\d+)*\\b/, operator: /!|&&|\\|\\||[<>]=?/, punctuation: /[(),]/ } }, literal: { pattern: /#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\\b/, alias: \"constant\" }, \"other-directive\": { pattern: /#\\w+\\b/, alias: \"property\" }, attribute: { pattern: /@\\w+/, alias: \"atrule\" }, \"function-definition\": { pattern: /(\\bfunc\\s+)\\w+/, lookbehind: true, alias: \"function\" }, label: { pattern: /\\b(break|continue)\\s+\\w+|\\b[a-zA-Z_]\\w*(?=\\s*:\\s*(?:for|repeat|while)\\b)/, lookbehind: true, alias: \"important\" }, keyword: /\\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\\b/, boolean: /\\b(?:false|true)\\b/, nil: { pattern: /\\bnil\\b/, alias: \"constant\" }, \"short-argument\": /\\$\\d+\\b/, omit: { pattern: /\\b_\\b/, alias: \"keyword\" }, number: /\\b(?:[\\d_]+(?:\\.[\\de_]+)?|0x[a-f0-9_]+(?:\\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\\b/i, \"class-name\": /\\b[A-Z](?:[A-Z_\\d]*[a-z]\\w*)?\\b/, function: /\\b[a-z_]\\w*(?=\\s*\\()/i, constant: /\\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\\b/, operator: /[-+*/%=!<>&|^~?]+|\\.[.\\-+*/%=!<>&|^~?]+/, punctuation: /[{}[\\]();,.:\\\\]/ }, Prism.languages.swift[\"string-literal\"].forEach(function(e) {\n  e.inside.interpolation.inside = Prism.languages.swift;\n}), function(e) {\n  e.languages.kotlin = e.languages.extend(\"clike\", { keyword: { pattern: /(^|[^.])\\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\\b/, lookbehind: true }, function: [{ pattern: /(?:`[^\\r\\n`]+`|\\b\\w+)(?=\\s*\\()/, greedy: true }, { pattern: /(\\.)(?:`[^\\r\\n`]+`|\\w+)(?=\\s*\\{)/, lookbehind: true, greedy: true }], number: /\\b(?:0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?[fFL]?)\\b/, operator: /\\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\\/*%<>]=?|[?:]:?|\\.\\.|&&|\\|\\||\\b(?:and|inv|or|shl|shr|ushr|xor)\\b/ }), delete e.languages.kotlin[\"class-name\"];\n  var n = { \"interpolation-punctuation\": { pattern: /^\\$\\{?|\\}$/, alias: \"punctuation\" }, expression: { pattern: /[\\s\\S]+/, inside: e.languages.kotlin } };\n  e.languages.insertBefore(\"kotlin\", \"string\", { \"string-literal\": [{ pattern: /\"\"\"(?:[^$]|\\$(?:(?!\\{)|\\{[^{}]*\\}))*?\"\"\"/, alias: \"multiline\", inside: { interpolation: { pattern: /\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i, inside: n }, string: /[\\s\\S]+/ } }, { pattern: /\"(?:[^\"\\\\\\r\\n$]|\\\\.|\\$(?:(?!\\{)|\\{[^{}]*\\}))*\"/, alias: \"singleline\", inside: { interpolation: { pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i, lookbehind: true, inside: n }, string: /[\\s\\S]+/ } }], char: { pattern: /'(?:[^'\\\\\\r\\n]|\\\\(?:.|u[a-fA-F0-9]{0,4}))'/, greedy: true } }), delete e.languages.kotlin.string, e.languages.insertBefore(\"kotlin\", \"keyword\", { annotation: { pattern: /\\B@(?:\\w+:)?(?:[A-Z]\\w*|\\[[^\\]]+\\])/, alias: \"builtin\" } }), e.languages.insertBefore(\"kotlin\", \"function\", { label: { pattern: /\\b\\w+@|@\\w+\\b/, alias: \"symbol\" } }), e.languages.kt = e.languages.kotlin, e.languages.kts = e.languages.kotlin;\n}(Prism), Prism.languages.c = Prism.languages.extend(\"clike\", { comment: { pattern: /\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/, greedy: true }, string: { pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/, greedy: true }, \"class-name\": { pattern: /(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/, lookbehind: true }, keyword: /\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/, function: /\\b[a-z_]\\w*(?=\\s*\\()/i, number: /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i, operator: />>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/ }), Prism.languages.insertBefore(\"c\", \"string\", { char: { pattern: /'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/, greedy: true } }), Prism.languages.insertBefore(\"c\", \"string\", { macro: { pattern: /(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im, lookbehind: true, greedy: true, alias: \"property\", inside: { string: [{ pattern: /^(#\\s*include\\s*)<[^>]+>/, lookbehind: true }, Prism.languages.c.string], char: Prism.languages.c.char, comment: Prism.languages.c.comment, \"macro-name\": [{ pattern: /(^#\\s*define\\s+)\\w+\\b(?!\\()/i, lookbehind: true }, { pattern: /(^#\\s*define\\s+)\\w+\\b(?=\\()/i, lookbehind: true, alias: \"function\" }], directive: { pattern: /^(#\\s*)[a-z]+/, lookbehind: true, alias: \"keyword\" }, \"directive-hash\": /^#/, punctuation: /##|\\\\(?=[\\r\\n])/, expression: { pattern: /\\S[\\s\\S]*/, inside: Prism.languages.c } } } }), Prism.languages.insertBefore(\"c\", \"function\", { constant: /\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/ }), delete Prism.languages.c.boolean, Prism.languages.objectivec = Prism.languages.extend(\"c\", { string: { pattern: /@?\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/, greedy: true }, keyword: /\\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\\b/, operator: /-[->]?|\\+\\+?|!=?|<<?=?|>>?=?|==?|&&?|\\|\\|?|[~^%?*\\/@]/ }), delete Prism.languages.objectivec[\"class-name\"], Prism.languages.objc = Prism.languages.objectivec, Prism.languages.reason = Prism.languages.extend(\"clike\", { string: { pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n\"])*\"/, greedy: true }, \"class-name\": /\\b[A-Z]\\w*/, keyword: /\\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\\b/, operator: /\\.{3}|:[:=]|\\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\\-*\\/]\\.?|\\b(?:asr|land|lor|lsl|lsr|lxor|mod)\\b/ }), Prism.languages.insertBefore(\"reason\", \"class-name\", { char: { pattern: /'(?:\\\\x[\\da-f]{2}|\\\\o[0-3][0-7][0-7]|\\\\\\d{3}|\\\\.|[^'\\\\\\r\\n])'/, greedy: true }, constructor: /\\b[A-Z]\\w*\\b(?!\\s*\\.)/, label: { pattern: /\\b[a-z]\\w*(?=::)/, alias: \"symbol\" } }), delete Prism.languages.reason.function, function(e) {\n  for (var n = /\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|<self>)*\\*\\//.source, t = 0; t < 2; t++)\n    n = n.replace(/<self>/g, function() {\n      return n;\n    });\n  n = n.replace(/<self>/g, function() {\n    return /[^\\s\\S]/.source;\n  }), e.languages.rust = { comment: [{ pattern: RegExp(/(^|[^\\\\])/.source + n), lookbehind: true, greedy: true }, { pattern: /(^|[^\\\\:])\\/\\/.*/, lookbehind: true, greedy: true }], string: { pattern: /b?\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|b?r(#*)\"(?:[^\"]|\"(?!\\1))*\"\\1/, greedy: true }, char: { pattern: /b?'(?:\\\\(?:x[0-7][\\da-fA-F]|u\\{(?:[\\da-fA-F]_*){1,6}\\}|.)|[^\\\\\\r\\n\\t'])'/, greedy: true }, attribute: { pattern: /#!?\\[(?:[^\\[\\]\"]|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")*\\]/, greedy: true, alias: \"attr-name\", inside: { string: null } }, \"closure-params\": { pattern: /([=(,:]\\s*|\\bmove\\s*)\\|[^|]*\\||\\|[^|]*\\|(?=\\s*(?:\\{|->))/, lookbehind: true, greedy: true, inside: { \"closure-punctuation\": { pattern: /^\\||\\|$/, alias: \"punctuation\" }, rest: null } }, \"lifetime-annotation\": { pattern: /'\\w+/, alias: \"symbol\" }, \"fragment-specifier\": { pattern: /(\\$\\w+:)[a-z]+/, lookbehind: true, alias: \"punctuation\" }, variable: /\\$\\w+/, \"function-definition\": { pattern: /(\\bfn\\s+)\\w+/, lookbehind: true, alias: \"function\" }, \"type-definition\": { pattern: /(\\b(?:enum|struct|trait|type|union)\\s+)\\w+/, lookbehind: true, alias: \"class-name\" }, \"module-declaration\": [{ pattern: /(\\b(?:crate|mod)\\s+)[a-z][a-z_\\d]*/, lookbehind: true, alias: \"namespace\" }, { pattern: /(\\b(?:crate|self|super)\\s*)::\\s*[a-z][a-z_\\d]*\\b(?:\\s*::(?:\\s*[a-z][a-z_\\d]*\\s*::)*)?/, lookbehind: true, alias: \"namespace\", inside: { punctuation: /::/ } }], keyword: [/\\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\\b/, /\\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\\b/], function: /\\b[a-z_]\\w*(?=\\s*(?:::\\s*<|\\())/, macro: { pattern: /\\b\\w+!/, alias: \"property\" }, constant: /\\b[A-Z_][A-Z_\\d]+\\b/, \"class-name\": /\\b[A-Z]\\w*\\b/, namespace: { pattern: /(?:\\b[a-z][a-z_\\d]*\\s*::\\s*)*\\b[a-z][a-z_\\d]*\\s*::(?!\\s*<)/, inside: { punctuation: /::/ } }, number: /\\b(?:0x[\\dA-Fa-f](?:_?[\\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\\d(?:_?\\d)*)?\\.)?\\d(?:_?\\d)*(?:[Ee][+-]?\\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\\b/, boolean: /\\b(?:false|true)\\b/, punctuation: /->|\\.\\.=|\\.{1,3}|::|[{}[\\];(),:]/, operator: /[-+*\\/%!^]=?|=[=>]?|&[&=]?|\\|[|=]?|<<?=?|>>?=?|[@?]/ }, e.languages.rust[\"closure-params\"].inside.rest = e.languages.rust, e.languages.rust.attribute.inside.string = e.languages.rust.string;\n}(Prism), Prism.languages.go = Prism.languages.extend(\"clike\", { string: { pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`[^`]*`/, lookbehind: true, greedy: true }, keyword: /\\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\\b/, boolean: /\\b(?:_|false|iota|nil|true)\\b/, number: [/\\b0(?:b[01_]+|o[0-7_]+)i?\\b/i, /\\b0x(?:[a-f\\d_]+(?:\\.[a-f\\d_]*)?|\\.[a-f\\d_]+)(?:p[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i, /(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?[\\d_]+)?i?(?!\\w)/i], operator: /[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\./, builtin: /\\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\\b/ }), Prism.languages.insertBefore(\"go\", \"string\", { char: { pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){0,10}'/, greedy: true } }), delete Prism.languages.go[\"class-name\"], function(e) {\n  var n = /\\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\\b/, t = /\\b(?!<keyword>)\\w+(?:\\s*\\.\\s*\\w+)*\\b/.source.replace(/<keyword>/g, function() {\n    return n.source;\n  });\n  e.languages.cpp = e.languages.extend(\"c\", { \"class-name\": [{ pattern: RegExp(/(\\b(?:class|concept|enum|struct|typename)\\s+)(?!<keyword>)\\w+/.source.replace(/<keyword>/g, function() {\n    return n.source;\n  })), lookbehind: true }, /\\b[A-Z]\\w*(?=\\s*::\\s*\\w+\\s*\\()/, /\\b[A-Z_]\\w*(?=\\s*::\\s*~\\w+\\s*\\()/i, /\\b\\w+(?=\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\\s*::\\s*\\w+\\s*\\()/], keyword: n, number: { pattern: /(?:\\b0b[01']+|\\b0x(?:[\\da-f']+(?:\\.[\\da-f']*)?|\\.[\\da-f']+)(?:p[+-]?[\\d']+)?|(?:\\b[\\d']+(?:\\.[\\d']*)?|\\B\\.[\\d']+)(?:e[+-]?[\\d']+)?)[ful]{0,4}/i, greedy: true }, operator: />>=?|<<=?|->|--|\\+\\+|&&|\\|\\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\\b/, boolean: /\\b(?:false|true)\\b/ }), e.languages.insertBefore(\"cpp\", \"string\", { module: { pattern: RegExp(/(\\b(?:import|module)\\s+)/.source + \"(?:\" + /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|<[^<>\\r\\n]*>/.source + \"|\" + /<mod-name>(?:\\s*:\\s*<mod-name>)?|:\\s*<mod-name>/.source.replace(/<mod-name>/g, function() {\n    return t;\n  }) + \")\"), lookbehind: true, greedy: true, inside: { string: /^[<\"][\\s\\S]+/, operator: /:/, punctuation: /\\./ } }, \"raw-string\": { pattern: /R\"([^()\\\\ ]{0,16})\\([\\s\\S]*?\\)\\1\"/, alias: \"string\", greedy: true } }), e.languages.insertBefore(\"cpp\", \"keyword\", { \"generic-function\": { pattern: /\\b(?!operator\\b)[a-z_]\\w*\\s*<(?:[^<>]|<[^<>]*>)*>(?=\\s*\\()/i, inside: { function: /^\\w+/, generic: { pattern: /<[\\s\\S]+/, alias: \"class-name\", inside: e.languages.cpp } } } }), e.languages.insertBefore(\"cpp\", \"operator\", { \"double-colon\": { pattern: /::/, alias: \"punctuation\" } }), e.languages.insertBefore(\"cpp\", \"class-name\", { \"base-clause\": { pattern: /(\\b(?:class|struct)\\s+\\w+\\s*:\\s*)[^;{}\"'\\s]+(?:\\s+[^;{}\"'\\s]+)*(?=\\s*[;{])/, lookbehind: true, greedy: true, inside: e.languages.extend(\"cpp\", {}) } }), e.languages.insertBefore(\"inside\", \"double-colon\", { \"class-name\": /\\b[a-z_]\\w*\\b(?!\\s*::)/i }, e.languages.cpp[\"base-clause\"]);\n}(Prism), Prism.languages.python = { comment: { pattern: /(^|[^\\\\])#.*/, lookbehind: true, greedy: true }, \"string-interpolation\": { pattern: /(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i, greedy: true, inside: { interpolation: { pattern: /((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/, lookbehind: true, inside: { \"format-spec\": { pattern: /(:)[^:(){}]+(?=\\}$)/, lookbehind: true }, \"conversion-option\": { pattern: /![sra](?=[:}]$)/, alias: \"punctuation\" }, rest: null } }, string: /[\\s\\S]+/ } }, \"triple-quoted-string\": { pattern: /(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i, greedy: true, alias: \"string\" }, string: { pattern: /(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i, greedy: true }, function: { pattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g, lookbehind: true }, \"class-name\": { pattern: /(\\bclass\\s+)\\w+/i, lookbehind: true }, decorator: { pattern: /(^[\\t ]*)@\\w+(?:\\.\\w+)*/m, lookbehind: true, alias: [\"annotation\", \"punctuation\"], inside: { punctuation: /\\./ } }, keyword: /\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/, builtin: /\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/, boolean: /\\b(?:False|None|True)\\b/, number: /\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i, operator: /[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/, punctuation: /[{}[\\];(),.:]/ }, Prism.languages.python[\"string-interpolation\"].inside.interpolation.inside.rest = Prism.languages.python, Prism.languages.py = Prism.languages.python, Prism.languages.json = { property: { pattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/, lookbehind: true, greedy: true }, string: { pattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?!\\s*:)/, lookbehind: true, greedy: true }, comment: { pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/, greedy: true }, number: /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i, punctuation: /[{}[\\],]/, operator: /:/, boolean: /\\b(?:false|true)\\b/, null: { pattern: /\\bnull\\b/, alias: \"keyword\" } }, Prism.languages.webmanifest = Prism.languages.json;\n\n// src/themes/index.ts\nvar themes_exports = {};\n__export(themes_exports, {\n  dracula: () => dracula_default,\n  duotoneDark: () => duotoneDark_default,\n  duotoneLight: () => duotoneLight_default,\n  github: () => github_default,\n  gruvboxMaterialDark: () => gruvboxMaterialDark_default,\n  gruvboxMaterialLight: () => gruvboxMaterialLight_default,\n  jettwaveDark: () => jettwaveDark_default,\n  jettwaveLight: () => jettwaveLight_default,\n  nightOwl: () => nightOwl_default,\n  nightOwlLight: () => nightOwlLight_default,\n  oceanicNext: () => oceanicNext_default,\n  okaidia: () => okaidia_default,\n  oneDark: () => oneDark_default,\n  oneLight: () => oneLight_default,\n  palenight: () => palenight_default,\n  shadesOfPurple: () => shadesOfPurple_default,\n  synthwave84: () => synthwave84_default,\n  ultramin: () => ultramin_default,\n  vsDark: () => vsDark_default,\n  vsLight: () => vsLight_default\n});\n\n// src/themes/dracula.ts\nvar theme = {\n  plain: {\n    color: \"#F8F8F2\",\n    backgroundColor: \"#282A36\"\n  },\n  styles: [\n    {\n      types: [\"prolog\", \"constant\", \"builtin\"],\n      style: {\n        color: \"rgb(189, 147, 249)\"\n      }\n    },\n    {\n      types: [\"inserted\", \"function\"],\n      style: {\n        color: \"rgb(80, 250, 123)\"\n      }\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        color: \"rgb(255, 85, 85)\"\n      }\n    },\n    {\n      types: [\"changed\"],\n      style: {\n        color: \"rgb(255, 184, 108)\"\n      }\n    },\n    {\n      types: [\"punctuation\", \"symbol\"],\n      style: {\n        color: \"rgb(248, 248, 242)\"\n      }\n    },\n    {\n      types: [\"string\", \"char\", \"tag\", \"selector\"],\n      style: {\n        color: \"rgb(255, 121, 198)\"\n      }\n    },\n    {\n      types: [\"keyword\", \"variable\"],\n      style: {\n        color: \"rgb(189, 147, 249)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(98, 114, 164)\"\n      }\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"rgb(241, 250, 140)\"\n      }\n    }\n  ]\n};\nvar dracula_default = theme;\n\n// src/themes/duotoneDark.ts\nvar theme2 = {\n  plain: {\n    backgroundColor: \"#2a2734\",\n    color: \"#9a86fd\"\n  },\n  styles: [\n    {\n      types: [\"comment\", \"prolog\", \"doctype\", \"cdata\", \"punctuation\"],\n      style: {\n        color: \"#6c6783\"\n      }\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        opacity: 0.7\n      }\n    },\n    {\n      types: [\"tag\", \"operator\", \"number\"],\n      style: {\n        color: \"#e09142\"\n      }\n    },\n    {\n      types: [\"property\", \"function\"],\n      style: {\n        color: \"#9a86fd\"\n      }\n    },\n    {\n      types: [\"tag-id\", \"selector\", \"atrule-id\"],\n      style: {\n        color: \"#eeebff\"\n      }\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"#c4b9fe\"\n      }\n    },\n    {\n      types: [\n        \"boolean\",\n        \"string\",\n        \"entity\",\n        \"url\",\n        \"attr-value\",\n        \"keyword\",\n        \"control\",\n        \"directive\",\n        \"unit\",\n        \"statement\",\n        \"regex\",\n        \"atrule\",\n        \"placeholder\",\n        \"variable\"\n      ],\n      style: {\n        color: \"#ffcc99\"\n      }\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        textDecorationLine: \"line-through\"\n      }\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        textDecorationLine: \"underline\"\n      }\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"important\", \"bold\"],\n      style: {\n        fontWeight: \"bold\"\n      }\n    },\n    {\n      types: [\"important\"],\n      style: {\n        color: \"#c4b9fe\"\n      }\n    }\n  ]\n};\nvar duotoneDark_default = theme2;\n\n// src/themes/duotoneLight.ts\nvar theme3 = {\n  plain: {\n    backgroundColor: \"#faf8f5\",\n    color: \"#728fcb\"\n  },\n  styles: [\n    {\n      types: [\"comment\", \"prolog\", \"doctype\", \"cdata\", \"punctuation\"],\n      style: {\n        color: \"#b6ad9a\"\n      }\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        opacity: 0.7\n      }\n    },\n    {\n      types: [\"tag\", \"operator\", \"number\"],\n      style: {\n        color: \"#063289\"\n      }\n    },\n    {\n      types: [\"property\", \"function\"],\n      style: {\n        color: \"#b29762\"\n      }\n    },\n    {\n      types: [\"tag-id\", \"selector\", \"atrule-id\"],\n      style: {\n        color: \"#2d2006\"\n      }\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"#896724\"\n      }\n    },\n    {\n      types: [\n        \"boolean\",\n        \"string\",\n        \"entity\",\n        \"url\",\n        \"attr-value\",\n        \"keyword\",\n        \"control\",\n        \"directive\",\n        \"unit\",\n        \"statement\",\n        \"regex\",\n        \"atrule\"\n      ],\n      style: {\n        color: \"#728fcb\"\n      }\n    },\n    {\n      types: [\"placeholder\", \"variable\"],\n      style: {\n        color: \"#93abdc\"\n      }\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        textDecorationLine: \"line-through\"\n      }\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        textDecorationLine: \"underline\"\n      }\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"important\", \"bold\"],\n      style: {\n        fontWeight: \"bold\"\n      }\n    },\n    {\n      types: [\"important\"],\n      style: {\n        color: \"#896724\"\n      }\n    }\n  ]\n};\nvar duotoneLight_default = theme3;\n\n// src/themes/github.ts\nvar theme4 = {\n  plain: {\n    color: \"#393A34\",\n    backgroundColor: \"#f6f8fa\"\n  },\n  styles: [\n    {\n      types: [\"comment\", \"prolog\", \"doctype\", \"cdata\"],\n      style: {\n        color: \"#999988\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        opacity: 0.7\n      }\n    },\n    {\n      types: [\"string\", \"attr-value\"],\n      style: {\n        color: \"#e3116c\"\n      }\n    },\n    {\n      types: [\"punctuation\", \"operator\"],\n      style: {\n        color: \"#393A34\"\n      }\n    },\n    {\n      types: [\n        \"entity\",\n        \"url\",\n        \"symbol\",\n        \"number\",\n        \"boolean\",\n        \"variable\",\n        \"constant\",\n        \"property\",\n        \"regex\",\n        \"inserted\"\n      ],\n      style: {\n        color: \"#36acaa\"\n      }\n    },\n    {\n      types: [\"atrule\", \"keyword\", \"attr-name\", \"selector\"],\n      style: {\n        color: \"#00a4db\"\n      }\n    },\n    {\n      types: [\"function\", \"deleted\", \"tag\"],\n      style: {\n        color: \"#d73a49\"\n      }\n    },\n    {\n      types: [\"function-variable\"],\n      style: {\n        color: \"#6f42c1\"\n      }\n    },\n    {\n      types: [\"tag\", \"selector\", \"keyword\"],\n      style: {\n        color: \"#00009f\"\n      }\n    }\n  ]\n};\nvar github_default = theme4;\n\n// src/themes/nightOwl.ts\nvar theme5 = {\n  plain: {\n    color: \"#d6deeb\",\n    backgroundColor: \"#011627\"\n  },\n  styles: [\n    {\n      types: [\"changed\"],\n      style: {\n        color: \"rgb(162, 191, 252)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        color: \"rgba(239, 83, 80, 0.56)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"inserted\", \"attr-name\"],\n      style: {\n        color: \"rgb(173, 219, 103)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(99, 119, 119)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"string\", \"url\"],\n      style: {\n        color: \"rgb(173, 219, 103)\"\n      }\n    },\n    {\n      types: [\"variable\"],\n      style: {\n        color: \"rgb(214, 222, 235)\"\n      }\n    },\n    {\n      types: [\"number\"],\n      style: {\n        color: \"rgb(247, 140, 108)\"\n      }\n    },\n    {\n      types: [\"builtin\", \"char\", \"constant\", \"function\"],\n      style: {\n        color: \"rgb(130, 170, 255)\"\n      }\n    },\n    {\n      // This was manually added after the auto-generation\n      // so that punctuations are not italicised\n      types: [\"punctuation\"],\n      style: {\n        color: \"rgb(199, 146, 234)\"\n      }\n    },\n    {\n      types: [\"selector\", \"doctype\"],\n      style: {\n        color: \"rgb(199, 146, 234)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"rgb(255, 203, 139)\"\n      }\n    },\n    {\n      types: [\"tag\", \"operator\", \"keyword\"],\n      style: {\n        color: \"rgb(127, 219, 202)\"\n      }\n    },\n    {\n      types: [\"boolean\"],\n      style: {\n        color: \"rgb(255, 88, 116)\"\n      }\n    },\n    {\n      types: [\"property\"],\n      style: {\n        color: \"rgb(128, 203, 196)\"\n      }\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        color: \"rgb(178, 204, 214)\"\n      }\n    }\n  ]\n};\nvar nightOwl_default = theme5;\n\n// src/themes/nightOwlLight.ts\nvar theme6 = {\n  plain: {\n    color: \"#403f53\",\n    backgroundColor: \"#FBFBFB\"\n  },\n  styles: [\n    {\n      types: [\"changed\"],\n      style: {\n        color: \"rgb(162, 191, 252)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        color: \"rgba(239, 83, 80, 0.56)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"inserted\", \"attr-name\"],\n      style: {\n        color: \"rgb(72, 118, 214)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(152, 159, 177)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"string\", \"builtin\", \"char\", \"constant\", \"url\"],\n      style: {\n        color: \"rgb(72, 118, 214)\"\n      }\n    },\n    {\n      types: [\"variable\"],\n      style: {\n        color: \"rgb(201, 103, 101)\"\n      }\n    },\n    {\n      types: [\"number\"],\n      style: {\n        color: \"rgb(170, 9, 130)\"\n      }\n    },\n    {\n      // This was manually added after the auto-generation\n      // so that punctuations are not italicised\n      types: [\"punctuation\"],\n      style: {\n        color: \"rgb(153, 76, 195)\"\n      }\n    },\n    {\n      types: [\"function\", \"selector\", \"doctype\"],\n      style: {\n        color: \"rgb(153, 76, 195)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"rgb(17, 17, 17)\"\n      }\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"rgb(153, 76, 195)\"\n      }\n    },\n    {\n      types: [\"operator\", \"property\", \"keyword\", \"namespace\"],\n      style: {\n        color: \"rgb(12, 150, 155)\"\n      }\n    },\n    {\n      types: [\"boolean\"],\n      style: {\n        color: \"rgb(188, 84, 84)\"\n      }\n    }\n  ]\n};\nvar nightOwlLight_default = theme6;\n\n// src/themes/oceanicNext.ts\nvar colors = {\n  char: \"#D8DEE9\",\n  comment: \"#999999\",\n  keyword: \"#c5a5c5\",\n  primitive: \"#5a9bcf\",\n  string: \"#8dc891\",\n  variable: \"#d7deea\",\n  boolean: \"#ff8b50\",\n  punctuation: \"#5FB3B3\",\n  tag: \"#fc929e\",\n  function: \"#79b6f2\",\n  className: \"#FAC863\",\n  method: \"#6699CC\",\n  operator: \"#fc929e\"\n};\nvar theme7 = {\n  plain: {\n    backgroundColor: \"#282c34\",\n    color: \"#ffffff\"\n  },\n  styles: [\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: colors.keyword\n      }\n    },\n    {\n      types: [\"attr-value\"],\n      style: {\n        color: colors.string\n      }\n    },\n    {\n      types: [\n        \"comment\",\n        \"block-comment\",\n        \"prolog\",\n        \"doctype\",\n        \"cdata\",\n        \"shebang\"\n      ],\n      style: {\n        color: colors.comment\n      }\n    },\n    {\n      types: [\n        \"property\",\n        \"number\",\n        \"function-name\",\n        \"constant\",\n        \"symbol\",\n        \"deleted\"\n      ],\n      style: {\n        color: colors.primitive\n      }\n    },\n    {\n      types: [\"boolean\"],\n      style: {\n        color: colors.boolean\n      }\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: colors.tag\n      }\n    },\n    {\n      types: [\"string\"],\n      style: {\n        color: colors.string\n      }\n    },\n    {\n      types: [\"punctuation\"],\n      style: {\n        color: colors.string\n      }\n    },\n    {\n      types: [\"selector\", \"char\", \"builtin\", \"inserted\"],\n      style: {\n        color: colors.char\n      }\n    },\n    {\n      types: [\"function\"],\n      style: {\n        color: colors.function\n      }\n    },\n    {\n      types: [\"operator\", \"entity\", \"url\", \"variable\"],\n      style: {\n        color: colors.variable\n      }\n    },\n    {\n      types: [\"keyword\"],\n      style: {\n        color: colors.keyword\n      }\n    },\n    {\n      types: [\"atrule\", \"class-name\"],\n      style: {\n        color: colors.className\n      }\n    },\n    {\n      types: [\"important\"],\n      style: {\n        fontWeight: \"400\"\n      }\n    },\n    {\n      types: [\"bold\"],\n      style: {\n        fontWeight: \"bold\"\n      }\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        opacity: 0.7\n      }\n    }\n  ]\n};\nvar oceanicNext_default = theme7;\n\n// src/themes/okaidia.ts\nvar theme8 = {\n  plain: {\n    color: \"#f8f8f2\",\n    backgroundColor: \"#272822\"\n  },\n  styles: [\n    {\n      types: [\"changed\"],\n      style: {\n        color: \"rgb(162, 191, 252)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        color: \"#f92672\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        color: \"rgb(173, 219, 103)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"#8292a2\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"string\", \"url\"],\n      style: {\n        color: \"#a6e22e\"\n      }\n    },\n    {\n      types: [\"variable\"],\n      style: {\n        color: \"#f8f8f2\"\n      }\n    },\n    {\n      types: [\"number\"],\n      style: {\n        color: \"#ae81ff\"\n      }\n    },\n    {\n      types: [\"builtin\", \"char\", \"constant\", \"function\", \"class-name\"],\n      style: {\n        color: \"#e6db74\"\n      }\n    },\n    {\n      types: [\"punctuation\"],\n      style: {\n        color: \"#f8f8f2\"\n      }\n    },\n    {\n      types: [\"selector\", \"doctype\"],\n      style: {\n        color: \"#a6e22e\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"tag\", \"operator\", \"keyword\"],\n      style: {\n        color: \"#66d9ef\"\n      }\n    },\n    {\n      types: [\"boolean\"],\n      style: {\n        color: \"#ae81ff\"\n      }\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        color: \"rgb(178, 204, 214)\",\n        opacity: 0.7\n      }\n    },\n    {\n      types: [\"tag\", \"property\"],\n      style: {\n        color: \"#f92672\"\n      }\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"#a6e22e !important\"\n      }\n    },\n    {\n      types: [\"doctype\"],\n      style: {\n        color: \"#8292a2\"\n      }\n    },\n    {\n      types: [\"rule\"],\n      style: {\n        color: \"#e6db74\"\n      }\n    }\n  ]\n};\nvar okaidia_default = theme8;\n\n// src/themes/palenight.ts\nvar theme9 = {\n  plain: {\n    color: \"#bfc7d5\",\n    backgroundColor: \"#292d3e\"\n  },\n  styles: [\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(105, 112, 152)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"string\", \"inserted\"],\n      style: {\n        color: \"rgb(195, 232, 141)\"\n      }\n    },\n    {\n      types: [\"number\"],\n      style: {\n        color: \"rgb(247, 140, 108)\"\n      }\n    },\n    {\n      types: [\"builtin\", \"char\", \"constant\", \"function\"],\n      style: {\n        color: \"rgb(130, 170, 255)\"\n      }\n    },\n    {\n      types: [\"punctuation\", \"selector\"],\n      style: {\n        color: \"rgb(199, 146, 234)\"\n      }\n    },\n    {\n      types: [\"variable\"],\n      style: {\n        color: \"rgb(191, 199, 213)\"\n      }\n    },\n    {\n      types: [\"class-name\", \"attr-name\"],\n      style: {\n        color: \"rgb(255, 203, 107)\"\n      }\n    },\n    {\n      types: [\"tag\", \"deleted\"],\n      style: {\n        color: \"rgb(255, 85, 114)\"\n      }\n    },\n    {\n      types: [\"operator\"],\n      style: {\n        color: \"rgb(137, 221, 255)\"\n      }\n    },\n    {\n      types: [\"boolean\"],\n      style: {\n        color: \"rgb(255, 88, 116)\"\n      }\n    },\n    {\n      types: [\"keyword\"],\n      style: {\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"doctype\"],\n      style: {\n        color: \"rgb(199, 146, 234)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        color: \"rgb(178, 204, 214)\"\n      }\n    },\n    {\n      types: [\"url\"],\n      style: {\n        color: \"rgb(221, 221, 221)\"\n      }\n    }\n  ]\n};\nvar palenight_default = theme9;\n\n// src/themes/shadesOfPurple.ts\nvar theme10 = {\n  plain: {\n    color: \"#9EFEFF\",\n    backgroundColor: \"#2D2A55\"\n  },\n  styles: [\n    {\n      types: [\"changed\"],\n      style: {\n        color: \"rgb(255, 238, 128)\"\n      }\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        color: \"rgba(239, 83, 80, 0.56)\"\n      }\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        color: \"rgb(173, 219, 103)\"\n      }\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(179, 98, 255)\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"punctuation\"],\n      style: {\n        color: \"rgb(255, 255, 255)\"\n      }\n    },\n    {\n      types: [\"constant\"],\n      style: {\n        color: \"rgb(255, 98, 140)\"\n      }\n    },\n    {\n      types: [\"string\", \"url\"],\n      style: {\n        color: \"rgb(165, 255, 144)\"\n      }\n    },\n    {\n      types: [\"variable\"],\n      style: {\n        color: \"rgb(255, 238, 128)\"\n      }\n    },\n    {\n      types: [\"number\", \"boolean\"],\n      style: {\n        color: \"rgb(255, 98, 140)\"\n      }\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"rgb(255, 180, 84)\"\n      }\n    },\n    {\n      types: [\n        \"keyword\",\n        \"operator\",\n        \"property\",\n        \"namespace\",\n        \"tag\",\n        \"selector\",\n        \"doctype\"\n      ],\n      style: {\n        color: \"rgb(255, 157, 0)\"\n      }\n    },\n    {\n      types: [\"builtin\", \"char\", \"constant\", \"function\", \"class-name\"],\n      style: {\n        color: \"rgb(250, 208, 0)\"\n      }\n    }\n  ]\n};\nvar shadesOfPurple_default = theme10;\n\n// src/themes/synthwave84.ts\nvar theme11 = {\n  plain: {\n    backgroundColor: \"linear-gradient(to bottom, #2a2139 75%, #34294f)\",\n    backgroundImage: \"#34294f\",\n    color: \"#f92aad\",\n    textShadow: \"0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3\"\n  },\n  styles: [\n    {\n      types: [\"comment\", \"block-comment\", \"prolog\", \"doctype\", \"cdata\"],\n      style: {\n        color: \"#495495\",\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"punctuation\"],\n      style: {\n        color: \"#ccc\"\n      }\n    },\n    {\n      types: [\n        \"tag\",\n        \"attr-name\",\n        \"namespace\",\n        \"number\",\n        \"unit\",\n        \"hexcode\",\n        \"deleted\"\n      ],\n      style: {\n        color: \"#e2777a\"\n      }\n    },\n    {\n      types: [\"property\", \"selector\"],\n      style: {\n        color: \"#72f1b8\",\n        textShadow: \"0 0 2px #100c0f, 0 0 10px #257c5575, 0 0 35px #21272475\"\n      }\n    },\n    {\n      types: [\"function-name\"],\n      style: {\n        color: \"#6196cc\"\n      }\n    },\n    {\n      types: [\"boolean\", \"selector-id\", \"function\"],\n      style: {\n        color: \"#fdfdfd\",\n        textShadow: \"0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975\"\n      }\n    },\n    {\n      types: [\"class-name\", \"maybe-class-name\", \"builtin\"],\n      style: {\n        color: \"#fff5f6\",\n        textShadow: \"0 0 2px #000, 0 0 10px #fc1f2c75, 0 0 5px #fc1f2c75, 0 0 25px #fc1f2c75\"\n      }\n    },\n    {\n      types: [\"constant\", \"symbol\"],\n      style: {\n        color: \"#f92aad\",\n        textShadow: \"0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3\"\n      }\n    },\n    {\n      types: [\"important\", \"atrule\", \"keyword\", \"selector-class\"],\n      style: {\n        color: \"#f4eee4\",\n        textShadow: \"0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575\"\n      }\n    },\n    {\n      types: [\"string\", \"char\", \"attr-value\", \"regex\", \"variable\"],\n      style: {\n        color: \"#f87c32\"\n      }\n    },\n    {\n      types: [\"parameter\"],\n      style: {\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"entity\", \"url\"],\n      style: {\n        color: \"#67cdcc\"\n      }\n    },\n    {\n      types: [\"operator\"],\n      style: {\n        color: \"ffffffee\"\n      }\n    },\n    {\n      types: [\"important\", \"bold\"],\n      style: {\n        fontWeight: \"bold\"\n      }\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"entity\"],\n      style: {\n        cursor: \"help\"\n      }\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        color: \"green\"\n      }\n    }\n  ]\n};\nvar synthwave84_default = theme11;\n\n// src/themes/ultramin.ts\nvar theme12 = {\n  plain: {\n    color: \"#282a2e\",\n    backgroundColor: \"#ffffff\"\n  },\n  styles: [\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(197, 200, 198)\"\n      }\n    },\n    {\n      types: [\"string\", \"number\", \"builtin\", \"variable\"],\n      style: {\n        color: \"rgb(150, 152, 150)\"\n      }\n    },\n    {\n      types: [\"class-name\", \"function\", \"tag\", \"attr-name\"],\n      style: {\n        color: \"rgb(40, 42, 46)\"\n      }\n    }\n  ]\n};\nvar ultramin_default = theme12;\n\n// src/themes/vsDark.ts\nvar theme13 = {\n  plain: {\n    color: \"#9CDCFE\",\n    backgroundColor: \"#1E1E1E\"\n  },\n  styles: [\n    {\n      types: [\"prolog\"],\n      style: {\n        color: \"rgb(0, 0, 128)\"\n      }\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(106, 153, 85)\"\n      }\n    },\n    {\n      types: [\"builtin\", \"changed\", \"keyword\", \"interpolation-punctuation\"],\n      style: {\n        color: \"rgb(86, 156, 214)\"\n      }\n    },\n    {\n      types: [\"number\", \"inserted\"],\n      style: {\n        color: \"rgb(181, 206, 168)\"\n      }\n    },\n    {\n      types: [\"constant\"],\n      style: {\n        color: \"rgb(100, 102, 149)\"\n      }\n    },\n    {\n      types: [\"attr-name\", \"variable\"],\n      style: {\n        color: \"rgb(156, 220, 254)\"\n      }\n    },\n    {\n      types: [\"deleted\", \"string\", \"attr-value\", \"template-punctuation\"],\n      style: {\n        color: \"rgb(206, 145, 120)\"\n      }\n    },\n    {\n      types: [\"selector\"],\n      style: {\n        color: \"rgb(215, 186, 125)\"\n      }\n    },\n    {\n      // Fix tag color\n      types: [\"tag\"],\n      style: {\n        color: \"rgb(78, 201, 176)\"\n      }\n    },\n    {\n      // Fix tag color for HTML\n      types: [\"tag\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"rgb(86, 156, 214)\"\n      }\n    },\n    {\n      types: [\"punctuation\", \"operator\"],\n      style: {\n        color: \"rgb(212, 212, 212)\"\n      }\n    },\n    {\n      // Fix punctuation color for HTML\n      types: [\"punctuation\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"#808080\"\n      }\n    },\n    {\n      types: [\"function\"],\n      style: {\n        color: \"rgb(220, 220, 170)\"\n      }\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"rgb(78, 201, 176)\"\n      }\n    },\n    {\n      types: [\"char\"],\n      style: {\n        color: \"rgb(209, 105, 105)\"\n      }\n    }\n  ]\n};\nvar vsDark_default = theme13;\n\n// src/themes/vsLight.ts\nvar theme14 = {\n  plain: {\n    color: \"#000000\",\n    backgroundColor: \"#ffffff\"\n  },\n  styles: [\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(0, 128, 0)\"\n      }\n    },\n    {\n      types: [\"builtin\"],\n      style: {\n        color: \"rgb(0, 112, 193)\"\n      }\n    },\n    {\n      types: [\"number\", \"variable\", \"inserted\"],\n      style: {\n        color: \"rgb(9, 134, 88)\"\n      }\n    },\n    {\n      types: [\"operator\"],\n      style: {\n        color: \"rgb(0, 0, 0)\"\n      }\n    },\n    {\n      types: [\"constant\", \"char\"],\n      style: {\n        color: \"rgb(129, 31, 63)\"\n      }\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"rgb(128, 0, 0)\"\n      }\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"rgb(255, 0, 0)\"\n      }\n    },\n    {\n      types: [\"deleted\", \"string\"],\n      style: {\n        color: \"rgb(163, 21, 21)\"\n      }\n    },\n    {\n      types: [\"changed\", \"punctuation\"],\n      style: {\n        color: \"rgb(4, 81, 165)\"\n      }\n    },\n    {\n      types: [\"function\", \"keyword\"],\n      style: {\n        color: \"rgb(0, 0, 255)\"\n      }\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"rgb(38, 127, 153)\"\n      }\n    }\n  ]\n};\nvar vsLight_default = theme14;\n\n// src/themes/jettwaveDark.ts\nvar theme15 = {\n  plain: {\n    color: \"#f8fafc\",\n    backgroundColor: \"#011627\"\n  },\n  styles: [\n    {\n      types: [\"prolog\"],\n      style: {\n        color: \"#000080\"\n      }\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"#6A9955\"\n      }\n    },\n    {\n      types: [\"builtin\", \"changed\", \"keyword\", \"interpolation-punctuation\"],\n      style: {\n        color: \"#569CD6\"\n      }\n    },\n    {\n      types: [\"number\", \"inserted\"],\n      style: {\n        color: \"#B5CEA8\"\n      }\n    },\n    {\n      types: [\"constant\"],\n      style: {\n        color: \"#f8fafc\"\n      }\n    },\n    {\n      types: [\"attr-name\", \"variable\"],\n      style: {\n        color: \"#9CDCFE\"\n      }\n    },\n    {\n      types: [\"deleted\", \"string\", \"attr-value\", \"template-punctuation\"],\n      style: {\n        color: \"#cbd5e1\"\n      }\n    },\n    {\n      types: [\"selector\"],\n      style: {\n        color: \"#D7BA7D\"\n      }\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"#0ea5e9\"\n      }\n    },\n    {\n      types: [\"tag\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"#0ea5e9\"\n      }\n    },\n    {\n      types: [\"punctuation\", \"operator\"],\n      style: {\n        color: \"#D4D4D4\"\n      }\n    },\n    {\n      types: [\"punctuation\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"#808080\"\n      }\n    },\n    {\n      types: [\"function\"],\n      style: {\n        color: \"#7dd3fc\"\n      }\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"#0ea5e9\"\n      }\n    },\n    {\n      types: [\"char\"],\n      style: {\n        color: \"#D16969\"\n      }\n    }\n  ]\n};\nvar jettwaveDark_default = theme15;\n\n// src/themes/jettwaveLight.ts\nvar theme16 = {\n  plain: {\n    color: \"#0f172a\",\n    backgroundColor: \"#f1f5f9\"\n  },\n  styles: [\n    {\n      types: [\"prolog\"],\n      style: {\n        color: \"#000080\"\n      }\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"#6A9955\"\n      }\n    },\n    {\n      types: [\"builtin\", \"changed\", \"keyword\", \"interpolation-punctuation\"],\n      style: {\n        color: \"#0c4a6e\"\n      }\n    },\n    {\n      types: [\"number\", \"inserted\"],\n      style: {\n        color: \"#B5CEA8\"\n      }\n    },\n    {\n      types: [\"constant\"],\n      style: {\n        color: \"#0f172a\"\n      }\n    },\n    {\n      types: [\"attr-name\", \"variable\"],\n      style: {\n        color: \"#0c4a6e\"\n      }\n    },\n    {\n      types: [\"deleted\", \"string\", \"attr-value\", \"template-punctuation\"],\n      style: {\n        color: \"#64748b\"\n      }\n    },\n    {\n      types: [\"selector\"],\n      style: {\n        color: \"#D7BA7D\"\n      }\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"#0ea5e9\"\n      }\n    },\n    {\n      types: [\"tag\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"#0ea5e9\"\n      }\n    },\n    {\n      types: [\"punctuation\", \"operator\"],\n      style: {\n        color: \"#475569\"\n      }\n    },\n    {\n      types: [\"punctuation\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"#808080\"\n      }\n    },\n    {\n      types: [\"function\"],\n      style: {\n        color: \"#0e7490\"\n      }\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"#0ea5e9\"\n      }\n    },\n    {\n      types: [\"char\"],\n      style: {\n        color: \"#D16969\"\n      }\n    }\n  ]\n};\nvar jettwaveLight_default = theme16;\n\n// src/themes/oneDark.ts\nvar theme17 = {\n  plain: {\n    backgroundColor: \"hsl(220, 13%, 18%)\",\n    color: \"hsl(220, 14%, 71%)\",\n    textShadow: \"0 1px rgba(0, 0, 0, 0.3)\"\n  },\n  styles: [\n    {\n      types: [\"comment\", \"prolog\", \"cdata\"],\n      style: {\n        color: \"hsl(220, 10%, 40%)\"\n      }\n    },\n    {\n      types: [\"doctype\", \"punctuation\", \"entity\"],\n      style: {\n        color: \"hsl(220, 14%, 71%)\"\n      }\n    },\n    {\n      types: [\n        \"attr-name\",\n        \"class-name\",\n        \"maybe-class-name\",\n        \"boolean\",\n        \"constant\",\n        \"number\",\n        \"atrule\"\n      ],\n      style: { color: \"hsl(29, 54%, 61%)\" }\n    },\n    {\n      types: [\"keyword\"],\n      style: { color: \"hsl(286, 60%, 67%)\" }\n    },\n    {\n      types: [\"property\", \"tag\", \"symbol\", \"deleted\", \"important\"],\n      style: {\n        color: \"hsl(355, 65%, 65%)\"\n      }\n    },\n    {\n      types: [\n        \"selector\",\n        \"string\",\n        \"char\",\n        \"builtin\",\n        \"inserted\",\n        \"regex\",\n        \"attr-value\"\n      ],\n      style: {\n        color: \"hsl(95, 38%, 62%)\"\n      }\n    },\n    {\n      types: [\"variable\", \"operator\", \"function\"],\n      style: {\n        color: \"hsl(207, 82%, 66%)\"\n      }\n    },\n    {\n      types: [\"url\"],\n      style: {\n        color: \"hsl(187, 47%, 55%)\"\n      }\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        textDecorationLine: \"line-through\"\n      }\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        textDecorationLine: \"underline\"\n      }\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"important\", \"bold\"],\n      style: {\n        fontWeight: \"bold\"\n      }\n    },\n    {\n      types: [\"important\"],\n      style: {\n        color: \"hsl(220, 14%, 71%)\"\n      }\n    }\n  ]\n};\nvar oneDark_default = theme17;\n\n// src/themes/oneLight.ts\nvar theme18 = {\n  plain: {\n    backgroundColor: \"hsl(230, 1%, 98%)\",\n    color: \"hsl(230, 8%, 24%)\"\n  },\n  styles: [\n    {\n      types: [\"comment\", \"prolog\", \"cdata\"],\n      style: {\n        color: \"hsl(230, 4%, 64%)\"\n      }\n    },\n    {\n      types: [\"doctype\", \"punctuation\", \"entity\"],\n      style: {\n        color: \"hsl(230, 8%, 24%)\"\n      }\n    },\n    {\n      types: [\n        \"attr-name\",\n        \"class-name\",\n        \"boolean\",\n        \"constant\",\n        \"number\",\n        \"atrule\"\n      ],\n      style: {\n        color: \"hsl(35, 99%, 36%)\"\n      }\n    },\n    {\n      types: [\"keyword\"],\n      style: {\n        color: \"hsl(301, 63%, 40%)\"\n      }\n    },\n    {\n      types: [\"property\", \"tag\", \"symbol\", \"deleted\", \"important\"],\n      style: {\n        color: \"hsl(5, 74%, 59%)\"\n      }\n    },\n    {\n      types: [\n        \"selector\",\n        \"string\",\n        \"char\",\n        \"builtin\",\n        \"inserted\",\n        \"regex\",\n        \"attr-value\",\n        \"punctuation\"\n      ],\n      style: {\n        color: \"hsl(119, 34%, 47%)\"\n      }\n    },\n    {\n      types: [\"variable\", \"operator\", \"function\"],\n      style: {\n        color: \"hsl(221, 87%, 60%)\"\n      }\n    },\n    {\n      types: [\"url\"],\n      style: {\n        color: \"hsl(198, 99%, 37%)\"\n      }\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        textDecorationLine: \"line-through\"\n      }\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        textDecorationLine: \"underline\"\n      }\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\"\n      }\n    },\n    {\n      types: [\"important\", \"bold\"],\n      style: {\n        fontWeight: \"bold\"\n      }\n    },\n    {\n      types: [\"important\"],\n      style: {\n        color: \"hsl(230, 8%, 24%)\"\n      }\n    }\n  ]\n};\nvar oneLight_default = theme18;\n\n// src/themes/gruvboxMaterialDark.ts\nvar theme19 = {\n  plain: {\n    color: \"#ebdbb2\",\n    backgroundColor: \"#292828\"\n  },\n  styles: [\n    {\n      types: [\n        \"imports\",\n        \"class-name\",\n        \"maybe-class-name\",\n        \"constant\",\n        \"doctype\",\n        \"builtin\",\n        \"function\"\n      ],\n      style: {\n        color: \"#d8a657\"\n      }\n    },\n    {\n      types: [\"property-access\"],\n      style: {\n        color: \"#7daea3\"\n      }\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"#e78a4e\"\n      }\n    },\n    {\n      types: [\"attr-name\", \"char\", \"url\", \"regex\"],\n      style: {\n        color: \"#a9b665\"\n      }\n    },\n    {\n      types: [\"attr-value\", \"string\"],\n      style: {\n        color: \"#89b482\"\n      }\n    },\n    {\n      types: [\"comment\", \"prolog\", \"cdata\", \"operator\", \"inserted\"],\n      style: {\n        color: \"#a89984\"\n      }\n    },\n    {\n      types: [\n        \"delimiter\",\n        \"boolean\",\n        \"keyword\",\n        \"selector\",\n        \"important\",\n        \"atrule\",\n        \"property\",\n        \"variable\",\n        \"deleted\"\n      ],\n      style: {\n        color: \"#ea6962\"\n      }\n    },\n    {\n      types: [\"entity\", \"number\", \"symbol\"],\n      style: {\n        color: \"#d3869b\"\n      }\n    }\n  ]\n};\nvar gruvboxMaterialDark_default = theme19;\n\n// src/themes/gruvboxMaterialLight.ts\nvar theme20 = {\n  plain: {\n    color: \"#654735\",\n    backgroundColor: \"#f9f5d7\"\n  },\n  styles: [\n    {\n      types: [\n        \"delimiter\",\n        \"boolean\",\n        \"keyword\",\n        \"selector\",\n        \"important\",\n        \"atrule\",\n        \"property\",\n        \"variable\",\n        \"deleted\"\n      ],\n      style: {\n        color: \"#af2528\"\n      }\n    },\n    {\n      types: [\n        \"imports\",\n        \"class-name\",\n        \"maybe-class-name\",\n        \"constant\",\n        \"doctype\",\n        \"builtin\"\n      ],\n      style: {\n        color: \"#b4730e\"\n      }\n    },\n    {\n      types: [\"string\", \"attr-value\"],\n      style: {\n        color: \"#477a5b\"\n      }\n    },\n    {\n      types: [\"property-access\"],\n      style: {\n        color: \"#266b79\"\n      }\n    },\n    {\n      types: [\"function\", \"attr-name\", \"char\", \"url\"],\n      style: {\n        color: \"#72761e\"\n      }\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"#b94c07\"\n      }\n    },\n    {\n      types: [\"comment\", \"prolog\", \"cdata\", \"operator\", \"inserted\"],\n      style: {\n        color: \"#a89984\"\n      }\n    },\n    {\n      types: [\"entity\", \"number\", \"symbol\"],\n      style: {\n        color: \"#924f79\"\n      }\n    }\n  ]\n};\nvar gruvboxMaterialLight_default = theme20;\n\n// src/index.ts\n\n\n// src/components/useGetLineProps.ts\n\n\nvar useGetLineProps = (themeDictionary) => (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n  (_a) => {\n    var _b = _a, { className, style, line } = _b, rest = __objRest(_b, [\"className\", \"style\", \"line\"]);\n    const output = __spreadProps(__spreadValues({}, rest), {\n      className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"token-line\", className)\n    });\n    if (typeof themeDictionary === \"object\" && \"plain\" in themeDictionary)\n      output.style = themeDictionary.plain;\n    if (typeof style === \"object\")\n      output.style = __spreadValues(__spreadValues({}, output.style || {}), style);\n    return output;\n  },\n  [themeDictionary]\n);\n\n// src/components/useGetTokenProps.ts\n\n\nvar useGetTokenProps = (themeDictionary) => {\n  const styleForToken = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    ({ types, empty }) => {\n      if (themeDictionary == null)\n        return void 0;\n      else if (types.length === 1 && types[0] === \"plain\") {\n        return empty != null ? { display: \"inline-block\" } : void 0;\n      } else if (types.length === 1 && empty != null) {\n        return themeDictionary[types[0]];\n      }\n      return Object.assign(\n        empty != null ? { display: \"inline-block\" } : {},\n        ...types.map((type) => themeDictionary[type])\n      );\n    },\n    [themeDictionary]\n  );\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (_a) => {\n      var _b = _a, { token, className, style } = _b, rest = __objRest(_b, [\"token\", \"className\", \"style\"]);\n      const output = __spreadProps(__spreadValues({}, rest), {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"token\", ...token.types, className),\n        children: token.content,\n        style: styleForToken(token)\n      });\n      if (style != null) {\n        output.style = __spreadValues(__spreadValues({}, output.style || {}), style);\n      }\n      return output;\n    },\n    [styleForToken]\n  );\n};\n\n// src/utils/normalizeTokens.ts\nvar newlineRe = /\\r\\n|\\r|\\n/;\nvar normalizeEmptyLines = (line) => {\n  if (line.length === 0) {\n    line.push({\n      types: [\"plain\"],\n      content: \"\\n\",\n      empty: true\n    });\n  } else if (line.length === 1 && line[0].content === \"\") {\n    line[0].content = \"\\n\";\n    line[0].empty = true;\n  }\n};\nvar appendTypes = (types, add) => {\n  const typesSize = types.length;\n  if (typesSize > 0 && types[typesSize - 1] === add) {\n    return types;\n  }\n  return types.concat(add);\n};\nvar normalizeTokens = (tokens) => {\n  const typeArrStack = [[]];\n  const tokenArrStack = [tokens];\n  const tokenArrIndexStack = [0];\n  const tokenArrSizeStack = [tokens.length];\n  let i = 0;\n  let stackIndex = 0;\n  let currentLine = [];\n  const acc = [currentLine];\n  while (stackIndex > -1) {\n    while ((i = tokenArrIndexStack[stackIndex]++) < tokenArrSizeStack[stackIndex]) {\n      let content;\n      let types = typeArrStack[stackIndex];\n      const tokenArr = tokenArrStack[stackIndex];\n      const token = tokenArr[i];\n      if (typeof token === \"string\") {\n        types = stackIndex > 0 ? types : [\"plain\"];\n        content = token;\n      } else {\n        types = appendTypes(types, token.type);\n        if (token.alias) {\n          types = appendTypes(types, token.alias);\n        }\n        content = token.content;\n      }\n      if (typeof content !== \"string\") {\n        stackIndex++;\n        typeArrStack.push(types);\n        tokenArrStack.push(content);\n        tokenArrIndexStack.push(0);\n        tokenArrSizeStack.push(content.length);\n        continue;\n      }\n      const splitByNewlines = content.split(newlineRe);\n      const newlineCount = splitByNewlines.length;\n      currentLine.push({\n        types,\n        content: splitByNewlines[0]\n      });\n      for (let i2 = 1; i2 < newlineCount; i2++) {\n        normalizeEmptyLines(currentLine);\n        acc.push(currentLine = []);\n        currentLine.push({\n          types,\n          content: splitByNewlines[i2]\n        });\n      }\n    }\n    stackIndex--;\n    typeArrStack.pop();\n    tokenArrStack.pop();\n    tokenArrIndexStack.pop();\n    tokenArrSizeStack.pop();\n  }\n  normalizeEmptyLines(currentLine);\n  return acc;\n};\nvar normalizeTokens_default = normalizeTokens;\n\n// src/components/useTokenize.ts\n\nvar useTokenize = ({ prism, code, grammar, language }) => {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (grammar == null)\n      return normalizeTokens_default([code]);\n    const prismConfig = {\n      code,\n      grammar,\n      language,\n      tokens: []\n    };\n    prism.hooks.run(\"before-tokenize\", prismConfig);\n    prismConfig.tokens = prism.tokenize(code, grammar);\n    prism.hooks.run(\"after-tokenize\", prismConfig);\n    return normalizeTokens_default(prismConfig.tokens);\n  }, [\n    code,\n    grammar,\n    language,\n    // prism is a stable import\n    prism\n  ]);\n};\n\n// src/utils/themeToDict.ts\nvar themeToDict = (theme21, language) => {\n  const { plain } = theme21;\n  const themeDict = theme21.styles.reduce((acc, themeEntry) => {\n    const { languages: languages2, style } = themeEntry;\n    if (languages2 && !languages2.includes(language)) {\n      return acc;\n    }\n    themeEntry.types.forEach((type) => {\n      const accStyle = __spreadValues(__spreadValues({}, acc[type]), style);\n      acc[type] = accStyle;\n    });\n    return acc;\n  }, {});\n  themeDict.root = plain;\n  themeDict.plain = __spreadProps(__spreadValues({}, plain), { backgroundColor: void 0 });\n  return themeDict;\n};\nvar themeToDict_default = themeToDict;\n\n// src/components/highlight.ts\nvar Highlight = ({\n  children,\n  language: _language,\n  code,\n  theme: theme21,\n  prism\n}) => {\n  const language = _language.toLowerCase();\n  const themeDictionary = themeToDict_default(theme21, language);\n  const getLineProps = useGetLineProps(themeDictionary);\n  const getTokenProps = useGetTokenProps(themeDictionary);\n  const grammar = prism.languages[language];\n  const tokens = useTokenize({ prism, language, code, grammar });\n  return children({\n    tokens,\n    className: `prism-code language-${language}`,\n    style: themeDictionary != null ? themeDictionary.root : {},\n    getLineProps,\n    getTokenProps\n  });\n};\n\n// src/index.ts\nvar Highlight2 = (props) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Highlight, __spreadProps(__spreadValues({}, props), {\n  prism: props.prism || Prism,\n  theme: props.theme || vsDark_default,\n  code: props.code,\n  language: props.language\n}));\n\n/*! Bundled license information:\n\nprismjs/prism.js:\n  (**\n   * Prism: Lightweight, robust, elegant syntax highlighting\n   *\n   * @license MIT <https://opensource.org/licenses/MIT>\n   * <AUTHOR> Verou <https://lea.verou.me>\n   * @namespace\n   * @public\n   *)\n*/\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prism-react-renderer/dist/index.mjs\n");

/***/ })

};
;