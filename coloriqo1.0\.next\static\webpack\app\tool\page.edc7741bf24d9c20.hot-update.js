"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tool/page",{

/***/ "(app-pages-browser)/./components/color-cards.tsx":
/*!************************************!*\
  !*** ./components/color-cards.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ColorCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pipette.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-panel-top.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hand.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Code,Copy,Cpu,CreditCard,Download,Hand,LayoutPanelTop,LogIn,LogOut,Pipette,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _hooks_use_color_extractor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-color-extractor */ \"(app-pages-browser)/./hooks/use-color-extractor.ts\");\n/* harmony import */ var _components_ui_loader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/loader */ \"(app-pages-browser)/./components/ui/loader.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _ui_code_preview__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/code-preview */ \"(app-pages-browser)/./components/ui/code-preview.tsx\");\n/* harmony import */ var _ui_magnifier__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/magnifier */ \"(app-pages-browser)/./components/ui/magnifier.tsx\");\n/* harmony import */ var _ui_theme_switcher__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ui/theme-switcher */ \"(app-pages-browser)/./components/ui/theme-switcher.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _lib_credit_service__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/credit-service */ \"(app-pages-browser)/./lib/credit-service.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ColorCards() {\n    _s();\n    // Constants\n    const COLOR_LIMIT_STANDARD = 10;\n    const ADMIN_CODE = \"creatordev\";\n    if (!ADMIN_CODE) {\n        throw new Error('NEXT_PUBLIC_ADMIN_CODE environment variable is required');\n    }\n    const COLORS_PER_CREDIT = 5 // Number of colors per credit\n    ;\n    // Constants for session storage\n    const SESSION_COLORS_KEY = \"color-tool-colors\";\n    const SESSION_IMAGE_KEY = \"color-tool-image\";\n    // State to track if session storage should be cleared\n    const [sessionCleared, setSessionCleared] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Memoize session storage operations with sessionCleared dependency\n    const storedColors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ColorCards.useMemo[storedColors]\": ()=>{\n            if ( true && !sessionCleared) {\n                try {\n                    const storedColors = sessionStorage.getItem(SESSION_COLORS_KEY);\n                    if (storedColors) {\n                        return JSON.parse(storedColors);\n                    }\n                } catch (error) {\n                // Silent fail - start with empty colors\n                }\n            }\n            return [];\n        }\n    }[\"ColorCards.useMemo[storedColors]\"], [\n        sessionCleared\n    ]);\n    // Memoize image retrieval with sessionCleared dependency\n    const storedImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ColorCards.useMemo[storedImage]\": ()=>{\n            if ( true && !sessionCleared) {\n                try {\n                    return sessionStorage.getItem(SESSION_IMAGE_KEY);\n                } catch (error) {\n                // Silent fail - start with no image\n                }\n            }\n            return null;\n        }\n    }[\"ColorCards.useMemo[storedImage]\"], [\n        sessionCleared\n    ]);\n    // States\n    const [extractedColors, setExtractedColors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(storedColors);\n    const [copiedColor, setCopiedColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [extractionMethod, setExtractionMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ai\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"upload\");\n    const [showCodePreview, setShowCodePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMagnifier, setShowMagnifier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPixelColor, setCurrentPixelColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAdminMode, setIsAdminMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [adminCodeInput, setAdminCodeInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showAdminInput, setShowAdminInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [creditState, setCreditState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ColorCards.useState\": ()=>(0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)()\n    }[\"ColorCards.useState\"]);\n    const [timeRemaining, setTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [manualPicksCount, setManualPicksCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // Count manual color picks\n    ;\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // State for mobile menu toggle\n    ;\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Create ref outside of useEffect to track credit state\n    const creditStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(creditState);\n    // Update ref whenever creditState changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            creditStateRef.current = creditState;\n        }\n    }[\"ColorCards.useEffect\"], [\n        creditState\n    ]);\n    // Update credit info and handle timer countdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            // Initial state fetch\n            const initialState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)(true);\n            setCreditState(initialState);\n            // Display and update timer\n            const timerInterval = setInterval({\n                \"ColorCards.useEffect.timerInterval\": ()=>{\n                    // Get current credit state\n                    const currentState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)(false);\n                    // If there's a regeneration time, show countdown\n                    if (currentState.nextRegenerationTime > 0) {\n                        // Calculate and display time remaining\n                        const timeLeft = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.formatTimeRemaining)(currentState.nextRegenerationTime);\n                        setTimeRemaining(timeLeft);\n                        // Check if time is up (credits should regenerate)\n                        if (timeLeft === \"now\") {\n                            // Force refresh to get updated credits\n                            const refreshedState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)(true);\n                            // Only update state if credits actually changed\n                            if (refreshedState.credits !== creditStateRef.current.credits) {\n                                setCreditState(refreshedState);\n                                // Show notification only if credits increased\n                                if (refreshedState.credits > creditStateRef.current.credits) {\n                                    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                                        title: \"Credits refreshed!\",\n                                        description: \"You have new credits available.\",\n                                        duration: 3000\n                                    });\n                                }\n                            }\n                        }\n                    } else {\n                        // No regeneration time, clear the timer display\n                        setTimeRemaining(\"\");\n                    }\n                    // Only update credit state if it has actually changed\n                    if (currentState.credits !== creditStateRef.current.credits || currentState.nextRegenerationTime !== creditStateRef.current.nextRegenerationTime) {\n                        setCreditState(currentState);\n                    }\n                }\n            }[\"ColorCards.useEffect.timerInterval\"], 1000); // Update every second\n            return ({\n                \"ColorCards.useEffect\": ()=>{\n                    clearInterval(timerInterval);\n                }\n            })[\"ColorCards.useEffect\"];\n        }\n    }[\"ColorCards.useEffect\"], []); // No dependencies to avoid re-creating the interval\n    // Update credit state - memoize this function to improve performance\n    const updateCreditState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ColorCards.useCallback[updateCreditState]\": function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            const newState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)(forceRefresh);\n            setCreditState(newState);\n            return newState;\n        }\n    }[\"ColorCards.useCallback[updateCreditState]\"], []);\n    // Show current credit status\n    const showCreditStatus = ()=>{\n        const state = updateCreditState();\n        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n            title: \"Credit Status\",\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Available credits: \",\n                            state.credits\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    state.nextRegenerationTime > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Credits will reset at: \",\n                            (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.formatTimeRemaining)(state.nextRegenerationTime)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 13\n                    }, this),\n                    state.nextRegenerationTime > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground mt-1\",\n                        children: (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getFullRegenerationTime)(state.nextRegenerationTime)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this),\n            duration: 7000\n        });\n    };\n    // Use a credit for extraction\n    const consumeCredit = ()=>{\n        if (isAdminMode) {\n            return true; // Admin has unlimited credits\n        }\n        // Get fresh state directly from service\n        const currentState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditState)(true);\n        if (currentState.credits > 0) {\n            try {\n                // Use credit and get new state\n                const newState = (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.useCredit)();\n                // Update component state\n                setCreditState(newState);\n                // Show toast to confirm credit usage\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Credit used\",\n                    description: \"1 credit consumed. \".concat(newState.credits, \" credits remaining.\"),\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Error\",\n                    description: \"Could not process credit. Please try again.\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n        return false;\n    };\n    // Update credits based on admin status\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            if (isAdminMode) {\n                // Reset credits if admin mode is activated\n                (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.resetCredits)();\n                updateCreditState();\n            }\n        }\n    }[\"ColorCards.useEffect\"], [\n        isAdminMode\n    ]);\n    // Listen for custom event to open admin input\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            const handleOpenAdminInput = {\n                \"ColorCards.useEffect.handleOpenAdminInput\": ()=>{\n                    setShowAdminInput(true);\n                }\n            }[\"ColorCards.useEffect.handleOpenAdminInput\"];\n            window.addEventListener('openAdminInput', handleOpenAdminInput);\n            return ({\n                \"ColorCards.useEffect\": ()=>{\n                    window.removeEventListener('openAdminInput', handleOpenAdminInput);\n                }\n            })[\"ColorCards.useEffect\"];\n        }\n    }[\"ColorCards.useEffect\"], []);\n    // Clean up any resources on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            return ({\n                \"ColorCards.useEffect\": ()=>{\n                    // Clean up any ObjectURLs or other resources\n                    if (selectedFile) {\n                        setSelectedFile(null);\n                    }\n                }\n            })[\"ColorCards.useEffect\"];\n        }\n    }[\"ColorCards.useEffect\"], []);\n    // Save colors to session storage when they change - debounced to reduce writes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            // Use a timeout to debounce multiple quick updates\n            const saveTimeout = setTimeout({\n                \"ColorCards.useEffect.saveTimeout\": ()=>{\n                    if ( true && extractedColors.length > 0) {\n                        try {\n                            sessionStorage.setItem(SESSION_COLORS_KEY, JSON.stringify(extractedColors));\n                        } catch (error) {\n                        // Silent fail - colors will be lost on refresh\n                        }\n                    }\n                }\n            }[\"ColorCards.useEffect.saveTimeout\"], 300); // Debounce for 300ms\n            // Clear timeout on cleanup\n            return ({\n                \"ColorCards.useEffect\": ()=>clearTimeout(saveTimeout)\n            })[\"ColorCards.useEffect\"];\n        }\n    }[\"ColorCards.useEffect\"], [\n        extractedColors\n    ]);\n    // Use our custom hook for color extraction\n    const { isExtracting, selectedImage, setSelectedImage, isPickingColor, extractionError, imageSize, toggleColorPicker, handleFileSelect, extractColors } = (0,_hooks_use_color_extractor__WEBPACK_IMPORTED_MODULE_7__.useColorExtractor)({\n        onExtractedColors: {\n            \"ColorCards.useColorExtractor\": (colors)=>{\n                setExtractedColors({\n                    \"ColorCards.useColorExtractor\": (prev)=>{\n                        const newColors = [\n                            ...prev,\n                            ...colors\n                        ];\n                        // Save to session storage if not cleared\n                        if (!sessionCleared && \"object\" !== 'undefined') {\n                            sessionStorage.setItem(SESSION_COLORS_KEY, JSON.stringify(newColors));\n                        }\n                        return newColors;\n                    }\n                }[\"ColorCards.useColorExtractor\"]);\n            }\n        }[\"ColorCards.useColorExtractor\"],\n        canvasRef,\n        existingColors: extractedColors,\n        isAdminMode,\n        initialImage: storedImage\n    });\n    // Save image to session storage when it changes - debounced\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorCards.useEffect\": ()=>{\n            // Debounce to prevent excessive writes\n            const saveTimeout = setTimeout({\n                \"ColorCards.useEffect.saveTimeout\": ()=>{\n                    if ( true && selectedImage && !sessionCleared) {\n                        try {\n                            sessionStorage.setItem(SESSION_IMAGE_KEY, selectedImage);\n                        } catch (error) {\n                        // Silent fail - image will be lost on refresh\n                        }\n                    }\n                }\n            }[\"ColorCards.useEffect.saveTimeout\"], 300); // Debounce for 300ms\n            return ({\n                \"ColorCards.useEffect\": ()=>clearTimeout(saveTimeout)\n            })[\"ColorCards.useEffect\"];\n        }\n    }[\"ColorCards.useEffect\"], [\n        selectedImage,\n        sessionCleared\n    ]);\n    // Handle mouse movement over the canvas\n    const handleMouseMove = (e)=>{\n        if (!canvasRef.current || extractionMethod !== \"manual\") return;\n        const canvas = canvasRef.current;\n        const rect = canvas.getBoundingClientRect();\n        // Calculate the cursor position relative to the canvas\n        const x = Math.min(Math.max(0, e.clientX - rect.left), canvas.width - 1);\n        const y = Math.min(Math.max(0, e.clientY - rect.top), canvas.height - 1);\n        setMousePosition({\n            x,\n            y\n        });\n        // Get the pixel color under the cursor\n        try {\n            const ctx = canvas.getContext(\"2d\");\n            if (ctx) {\n                const pixelData = ctx.getImageData(x, y, 1, 1).data;\n                const pixelColor = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.rgbToHex)(pixelData[0], pixelData[1], pixelData[2]);\n                setCurrentPixelColor(pixelColor);\n            }\n        } catch (error) {\n        // Silent fail - color picker will not show current color\n        }\n    };\n    // Calculate magnifier position to keep it within viewport bounds\n    const getMagnifierPosition = (x, y, canvasRect, size)=>{\n        const viewportWidth = window.innerWidth;\n        const viewportHeight = window.innerHeight;\n        // Default position (to the right of cursor)\n        let posX = x + 20;\n        let posY = y - 75;\n        // Adjust if too close to right edge\n        if (posX + size > viewportWidth - 20) {\n            posX = x - size - 20 // Position to the left of cursor\n            ;\n        }\n        // Adjust if too close to bottom edge\n        if (posY + size > viewportHeight - 20) {\n            posY = viewportHeight - size - 20;\n        }\n        // Adjust if too close to top edge\n        if (posY < 20) {\n            posY = 20;\n        }\n        return {\n            left: posX,\n            top: posY\n        };\n    };\n    // Handle mouse enter on canvas\n    const handleMouseEnter = ()=>{\n        if (extractionMethod === \"manual\" && isPickingColor && (isAdminMode || creditState.credits > 0)) {\n            setShowMagnifier(true);\n        }\n    };\n    // Handle mouse leave on canvas\n    const handleMouseLeave = ()=>{\n        setShowMagnifier(false);\n        setMousePosition(null);\n        setCurrentPixelColor(\"\");\n    };\n    // Get all colors for display and export - memoized to prevent recalculations\n    const allColors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ColorCards.useMemo[allColors]\": ()=>extractedColors\n    }[\"ColorCards.useMemo[allColors]\"], [\n        extractedColors\n    ]);\n    // Trigger file input click\n    const handleUploadClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    // Handle file change with additional security checks\n    const handleFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            // Check file size (limit to 5MB)\n            const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\n            if (file.size > MAX_FILE_SIZE) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"File too large\",\n                    description: \"The image must be smaller than 5MB\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Check file type - expanded list of supported formats\n            const allowedTypes = [\n                'image/jpeg',\n                'image/png',\n                'image/webp',\n                'image/gif',\n                'image/bmp',\n                'image/tiff',\n                'image/svg+xml',\n                'image/x-icon',\n                'image/vnd.microsoft.icon',\n                'image/heic',\n                'image/heif',\n                'image/avif',\n                'image/jp2',\n                'image/jpx',\n                'image/jpm',\n                'image/jxl'\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Invalid file type\",\n                    description: \"Please upload a supported image format (JPEG, PNG, WebP, GIF, BMP, TIFF, SVG, ICO, HEIC, AVIF, JPEG2000, JPEG XL)\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Validate image dimensions\n            const img = new Image();\n            img.onload = ()=>{\n                const MAX_DIMENSION = 4096; // Maximum width or height\n                if (img.width > MAX_DIMENSION || img.height > MAX_DIMENSION) {\n                    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                        title: \"Image too large\",\n                        description: \"Image dimensions must be less than 4096x4096 pixels\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const selectedFile = handleFileSelect(file);\n                if (selectedFile) {\n                    setSelectedFile(selectedFile);\n                }\n            };\n            img.onerror = ()=>{\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Invalid image\",\n                    description: \"The file appears to be corrupted or invalid\",\n                    variant: \"destructive\"\n                });\n            };\n            img.src = URL.createObjectURL(file);\n        }\n    };\n    // Name colors using AI\n    const nameColorsWithAI = async (hexColors)=>{\n        try {\n            const response = await fetch(\"/api/name-colors\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    colors: hexColors\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to name colors\");\n            }\n            const data = await response.json();\n            return data.colors;\n        } catch (error) {\n            // Fallback to local naming if AI fails\n            return hexColors.map((hex)=>({\n                    hex,\n                    name: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.getColorName)(hex)\n                }));\n        }\n    };\n    // Handle starting color extraction with the selected method\n    const handleExtract = async ()=>{\n        if (selectedFile) {\n            // Force refresh credit state before checking\n            const freshState = updateCreditState(true);\n            // Check if we have credits available or are in admin mode\n            if (isAdminMode || freshState.credits > 0) {\n                // For AI extraction, we'll consume 1 credit for up to 5 colors\n                if (extractionMethod === \"ai\" && !isAdminMode) {\n                    // Always consume a credit BEFORE extraction for AI method\n                    const creditConsumed = consumeCredit();\n                    if (!creditConsumed) {\n                        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                            title: \"Error\",\n                            description: \"Could not process credit. Please try again.\",\n                            variant: \"destructive\",\n                            duration: 3000\n                        });\n                        return;\n                    }\n                    // Show a toast notification about AI extracting colors\n                    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                        title: \"AI is analyzing your image\",\n                        description: \"Extracting the most prominent and visually important colors...\",\n                        duration: 5000\n                    });\n                    await extractColors(selectedFile, extractionMethod);\n                } else {\n                    await extractColors(selectedFile, extractionMethod);\n                }\n            } else {\n                // If no credits, show toast with info about regeneration\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"No credits available\",\n                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Credits will be available at \",\n                                    timeRemaining,\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs mt-1 cursor-pointer text-blue-500\",\n                                onClick: toggleAdminInput,\n                                children: \"Upgrade for unlimited access\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 13\n                    }, this),\n                    duration: 5000\n                });\n            }\n        } else {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"No image selected\",\n                description: \"Please upload an image first.\",\n                variant: \"destructive\",\n                duration: 3000\n            });\n        }\n    };\n    // Extract a single color from the canvas when clicked\n    const handleCanvasClick = async (e)=>{\n        if (!canvasRef.current) return;\n        // Check if we're in manual mode, if not, don't do anything\n        if (extractionMethod !== \"manual\") return;\n        // Check if we have credits available or are in admin mode\n        if (!isAdminMode && creditState.credits === 0) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"No credits available\",\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Credits will be available at \",\n                                timeRemaining,\n                                \".\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs mt-1 cursor-pointer text-blue-500\",\n                            onClick: toggleAdminInput,\n                            children: \"Upgrade for unlimited access\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, this),\n                duration: 5000\n            });\n            return;\n        }\n        try {\n            const canvas = canvasRef.current;\n            const rect = canvas.getBoundingClientRect();\n            const x = Math.min(Math.max(0, e.clientX - rect.left), canvas.width - 1);\n            const y = Math.min(Math.max(0, e.clientY - rect.top), canvas.height - 1);\n            const ctx = canvas.getContext(\"2d\");\n            if (ctx) {\n                const pixelData = ctx.getImageData(x, y, 1, 1).data;\n                const hex = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.rgbToHex)(pixelData[0], pixelData[1], pixelData[2]);\n                // Check if this color already exists\n                if (extractedColors.some((color)=>color.hex.toLowerCase() === hex.toLowerCase())) {\n                    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                        title: \"Duplicate color\",\n                        description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-4 rounded-full border\",\n                                    style: {\n                                        backgroundColor: hex\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"This color is already in your palette\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 15\n                        }, this),\n                        duration: 3000\n                    });\n                    return;\n                }\n                // For manual picking, only consume a credit every COLORS_PER_CREDIT colors\n                if (!isAdminMode) {\n                    const newPicksCount = manualPicksCount + 1;\n                    setManualPicksCount(newPicksCount);\n                    // If we've reached the threshold, consume a credit and reset the counter\n                    if (newPicksCount >= COLORS_PER_CREDIT) {\n                        const creditConsumed = consumeCredit();\n                        if (!creditConsumed) {\n                            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                                title: \"Credit deduction failed\",\n                                description: \"Could not deduct credit. Please try again.\",\n                                variant: \"destructive\",\n                                duration: 3000\n                            });\n                            return;\n                        }\n                        setManualPicksCount(0);\n                    // Credit usage toast is now handled in consumeCredit function\n                    } else {\n                        // Show remaining picks\n                        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                            title: \"Color extracted!\",\n                            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 w-4 rounded-full border\",\n                                                style: {\n                                                    backgroundColor: hex\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: hex\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            COLORS_PER_CREDIT - newPicksCount,\n                                            \" more picks until 1 credit is used\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 17\n                            }, this),\n                            duration: 2000\n                        });\n                    }\n                }\n                // Set a loading state\n                const tempColorId = Date.now().toString();\n                const tempColor = {\n                    name: \"Naming...\",\n                    hex,\n                    id: tempColorId\n                };\n                setExtractedColors((prev)=>[\n                        ...prev,\n                        tempColor\n                    ]);\n                // Get color name from AI\n                try {\n                    const namedColors = await nameColorsWithAI([\n                        hex\n                    ]);\n                    if (namedColors && namedColors.length > 0) {\n                        // Update the color with the AI-generated name\n                        setExtractedColors((prev)=>prev.map((color)=>color.id === tempColorId ? {\n                                    ...namedColors[0],\n                                    id: tempColorId\n                                } : color));\n                        // Only show toast if we're in admin mode (otherwise it's shown in the credit handling code)\n                        if (isAdminMode) {\n                            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                                title: \"Color extracted!\",\n                                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 w-4 rounded-full border\",\n                                            style: {\n                                                backgroundColor: hex\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                namedColors[0].name,\n                                                \" (\",\n                                                hex,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 19\n                                }, this),\n                                duration: 3000\n                            });\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"Error getting color name:\", error);\n                    // If AI naming fails, use the local getColorName function as fallback\n                    const colorName = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.getColorName)(hex);\n                    setExtractedColors((prev)=>prev.map((color)=>color.id === tempColorId ? {\n                                name: colorName,\n                                hex,\n                                id: tempColorId\n                            } : color));\n                    // Only show toast if we're in admin mode (otherwise it's shown in the credit handling code)\n                    if (isAdminMode) {\n                        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                            title: \"Color extracted!\",\n                            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-4 rounded-full border\",\n                                        style: {\n                                            backgroundColor: hex\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            colorName,\n                                            \" (\",\n                                            hex,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 17\n                            }, this),\n                            duration: 3000\n                        });\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Error extracting color:\", error);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Error extracting color\",\n                description: \"Could not read pixel data from the image\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Copy color to clipboard with error handling\n    const copyColor = (hex)=>{\n        try {\n            navigator.clipboard.writeText(hex).then(()=>{\n                setCopiedColor(hex);\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Color copied!\",\n                    description: \"\".concat(hex, \" has been copied to clipboard.\"),\n                    duration: 2000\n                });\n                // Reset copied state after 2 seconds\n                setTimeout(()=>{\n                    setCopiedColor(null);\n                }, 2000);\n            }).catch(()=>{\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                    title: \"Failed to copy\",\n                    description: \"Could not copy to clipboard. Try again or copy manually.\",\n                    variant: \"destructive\"\n                });\n            });\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Failed to copy\",\n                description: \"Could not copy to clipboard. Try again or copy manually.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Show code preview \n    const handleShowCodePreview = ()=>{\n        if (allColors.length === 0) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"No colors to export\",\n                description: \"Extract some colors first before viewing code.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Color codes should always be available to view (not premium)\n        setShowCodePreview(true);\n    };\n    // Export palette as a file\n    const exportPalette = ()=>{\n        if (allColors.length === 0) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"No colors to export\",\n                description: \"Extract some colors first before exporting.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const jsonContent = JSON.stringify(allColors, null, 2);\n            const blob = new Blob([\n                jsonContent\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"color-palette.json\";\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Palette exported!\",\n                description: \"Color palette has been exported as JSON file.\",\n                duration: 3000\n            });\n        } catch (error) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Export failed\",\n                description: \"Could not export the color palette. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Remove a color from extracted colors\n    const removeColor = (index)=>{\n        setExtractedColors((prev)=>prev.filter((_, i)=>i !== index));\n        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n            title: \"Color removed\",\n            description: \"The color has been removed from your palette.\",\n            duration: 2000\n        });\n    };\n    // Clear all extracted colors\n    const clearAllColors = ()=>{\n        if (extractedColors.length === 0) return;\n        setExtractedColors([]);\n        // Clear colors from session storage\n        if (true) {\n            sessionStorage.removeItem(SESSION_COLORS_KEY);\n        }\n        setSessionCleared(true);\n        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n            title: \"Colors cleared\",\n            description: \"All extracted colors have been cleared.\",\n            duration: 2000\n        });\n    };\n    // Remove the image\n    const removeImage = ()=>{\n        setSelectedFile(null);\n        setSelectedImage(null);\n        // Clear image from session storage\n        if (true) {\n            sessionStorage.removeItem(SESSION_IMAGE_KEY);\n        }\n        setSessionCleared(true);\n        (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n            title: \"Image removed\",\n            description: \"The image has been removed.\",\n            duration: 2000\n        });\n    };\n    // Toggle the color picker with magnifier setup\n    const handleToggleColorPicker = ()=>{\n        const newState = !isPickingColor;\n        toggleColorPicker();\n        // If we're turning off the color picker, hide magnifier\n        if (!newState) {\n            setShowMagnifier(false);\n        }\n    };\n    // Handle extraction method change\n    const handleExtractionMethodChange = (value)=>{\n        const method = value;\n        setExtractionMethod(method);\n        if (method === \"manual\") {\n            // Always enable color picking mode when switching to manual\n            if (!isPickingColor) {\n                toggleColorPicker();\n            }\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Manual mode activated\",\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Click anywhere on the image to pick colors\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 862,\n                    columnNumber: 11\n                }, this),\n                duration: 3000\n            });\n        } else {\n            // When switching to AI mode, hide magnifier and disable color picking if active\n            setShowMagnifier(false);\n            if (isPickingColor) {\n                toggleColorPicker();\n            }\n        }\n    };\n    // Verify admin code\n    const verifyAdminCode = (code)=>{\n        if (code === ADMIN_CODE) {\n            setIsAdminMode(true);\n            setShowAdminInput(false);\n            // Reset credits for admin (unlimited)\n            (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.resetCredits)();\n            updateCreditState();\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Admin mode activated\",\n                description: \"No color extraction limits applied\",\n                duration: 3000\n            });\n        } else {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: \"Invalid code\",\n                description: \"The code entered is not valid\",\n                variant: \"destructive\",\n                duration: 3000\n            });\n        }\n        setAdminCodeInput(\"\");\n    };\n    // Toggle admin input visibility\n    const toggleAdminInput = ()=>{\n        setShowAdminInput((prev)=>!prev);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 flex flex-col bg-background overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"border-b bg-card p-4 flex items-center justify-between shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 ml-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-6 w-6 text-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md\",\n                                                    style: {\n                                                        maskImage: 'url(\"data:image/svg+xml,%3Csvg xmlns=\\'http://www.w3.org/2000/svg\\' width=\\'24\\' height=\\'24\\' viewBox=\\'0 0 24 24\\' fill=\\'none\\' stroke=\\'currentColor\\' stroke-width=\\'2\\' stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\'%3E%3Cpath d=\\'M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121\\'/%3E%3C/svg%3E\")',\n                                                        maskSize: 'cover'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 917,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400\",\n                                            style: {\n                                                fontFamily: \"var(--font-montserrat)\",\n                                                letterSpacing: \"-0.5px\",\n                                                fontWeight: \"800\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_15___default()), {\n                                                href: \"/\",\n                                                \"data-barba\": \"wrapper\",\n                                                children: \"Coloriqo\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 920,\n                                                columnNumber: 16\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 919,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 914,\n                                    columnNumber: 13\n                                }, this),\n                                isAdminMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full font-medium\",\n                                    children: \"Admin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-3 py-1 border rounded-md bg-background\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: [\n                                                isAdminMode ? \"∞\" : creditState.credits,\n                                                \" Credits\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 15\n                                        }, this),\n                                        creditState.credits === 0 && !isAdminMode && timeRemaining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-xs text-muted-foreground ml-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Available at \",\n                                                        timeRemaining\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 940,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 938,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isAdminMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            className: \"h-6 ml-1 px-2 text-xs text-blue-500\",\n                                            onClick: showCreditStatus,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 950,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Status\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_theme_switcher__WEBPACK_IMPORTED_MODULE_12__.ThemeSwitcher, {}, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdminMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-200\",\n                                            onClick: ()=>{\n                                                setIsAdminMode(false);\n                                                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                                                    title: \"Logged out\",\n                                                    description: \"Returned to standard user mode\",\n                                                    duration: 3000\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 971,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Admin Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: toggleAdminInput,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 979,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Admin Login\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 974,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleShowCodePreview,\n                                            disabled: extractedColors.length === 0 ? true : false,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 989,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" View Code\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 983,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: exportPalette,\n                                            disabled: extractedColors.length === 0 ? true : false,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 997,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" Export\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 991,\n                                            columnNumber: 15\n                                        }, this),\n                                        extractedColors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: clearAllColors,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Clear All\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 17\n                                        }, this) : null\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 930,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex md:hidden items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1 px-2 py-1 border rounded-md bg-background text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-3 w-3 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: isAdminMode ? \"∞\" : creditState.credits\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1012,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1010,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_theme_switcher__WEBPACK_IMPORTED_MODULE_12__.ThemeSwitcher, {}, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1018,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    className: \"h-9 w-9 relative overflow-hidden group hover:bg-accent transition-colors\",\n                                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                    \"aria-label\": \"Toggle navigation menu\",\n                                    \"aria-expanded\": isMobileMenuOpen,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col justify-center items-center w-5 h-5 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform \".concat(isMobileMenuOpen ? 'rotate-45 translate-y-0' : '-translate-y-1.5')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out \".concat(isMobileMenuOpen ? 'opacity-0 scale-0' : 'opacity-100 scale-100')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform \".concat(isMobileMenuOpen ? '-rotate-45 translate-y-0' : 'translate-y-1.5')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 1008,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden fixed inset-0 z-50 transition-all duration-300 \".concat(isMobileMenuOpen ? 'visible' : 'invisible'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-black/50 transition-opacity duration-300 \".concat(isMobileMenuOpen ? 'opacity-100' : 'opacity-0'),\n                                    onClick: ()=>setIsMobileMenuOpen(false)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1057,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 top-0 h-full w-80 bg-background border-l shadow-lg transform transition-transform duration-300 \".concat(isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: \"Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1066,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"h-8 w-8 hover:bg-accent transition-colors\",\n                                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                                    \"aria-label\": \"Close menu\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-4 h-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute block h-0.5 w-4 bg-current rounded-full rotate-45 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1075,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute block h-0.5 w-4 bg-current rounded-full -rotate-45 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1076,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1074,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1065,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col p-4 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Admin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1085,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isAdminMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-200\",\n                                                            onClick: ()=>{\n                                                                setIsAdminMode(false);\n                                                                setIsMobileMenuOpen(false);\n                                                                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                                                                    title: \"Logged out\",\n                                                                    description: \"Returned to standard user mode\",\n                                                                    duration: 3000\n                                                                });\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1101,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Admin Logout\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1087,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            onClick: ()=>{\n                                                                toggleAdminInput();\n                                                                setIsMobileMenuOpen(false);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1113,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Admin Login\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1104,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1084,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Tools\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1120,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            onClick: ()=>{\n                                                                handleShowCodePreview();\n                                                                setIsMobileMenuOpen(false);\n                                                            },\n                                                            disabled: extractedColors.length === 0 ? true : false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1131,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" View Code\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1121,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            onClick: ()=>{\n                                                                exportPalette();\n                                                                setIsMobileMenuOpen(false);\n                                                            },\n                                                            disabled: extractedColors.length === 0 ? true : false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1143,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" Export\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1133,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        extractedColors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            onClick: ()=>{\n                                                                clearAllColors();\n                                                                setIsMobileMenuOpen(false);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1155,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Clear All\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1146,\n                                                            columnNumber: 21\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1119,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Credits\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1162,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 px-3 py-2 border rounded-md bg-background\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1164,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: [\n                                                                        isAdminMode ? \"∞\" : creditState.credits,\n                                                                        \" Credits\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1165,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1163,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        creditState.credits === 0 && !isAdminMode && timeRemaining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-xs text-muted-foreground px-3 py-2 border rounded-md bg-muted/50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-3 w-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1171,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Available at \",\n                                                                        timeRemaining\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1172,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1170,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        !isAdminMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"w-full text-blue-500\",\n                                                            onClick: ()=>{\n                                                                showCreditStatus();\n                                                                setIsMobileMenuOpen(false);\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1185,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Check Status\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1176,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1161,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1082,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 1055,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 911,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                            className: \"w-64 border-r bg-muted/40 p-4 flex flex-col overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                defaultValue: \"upload\",\n                                value: activeTab,\n                                onValueChange: setActiveTab,\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"upload\",\n                                                className: \"flex-1\",\n                                                children: \"Upload\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"palette\",\n                                                className: \"flex-1\",\n                                                children: \"Palette\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"upload\",\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: handleUploadClick,\n                                                    variant: \"default\",\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1207,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Upload Image\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1206,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    ref: fileInputRef,\n                                                    onChange: handleFileChange,\n                                                    accept: \"image/jpeg,image/png,image/webp,image/gif,image/bmp,image/tiff,image/svg+xml,image/x-icon,image/vnd.microsoft.icon,image/heic,image/heif,image/avif,image/jp2,image/jpx,image/jpm,image/jxl\",\n                                                    className: \"hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1209,\n                                                    columnNumber: 19\n                                                }, this),\n                                                selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1219,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Extraction Method\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1220,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                                            defaultValue: \"ai\",\n                                                            className: \"w-full\",\n                                                            onValueChange: (value)=>handleExtractionMethodChange(value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                                                    className: \"grid w-full grid-cols-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                            value: \"ai\",\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1225,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \" AI\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1224,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                                            value: \"manual\",\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1228,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \" Manual\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1227,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1223,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                    value: \"ai\",\n                                                                    className: \"mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mb-2\",\n                                                                            children: \"Extract multiple colors at once using AI.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1233,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        creditState.credits === 0 && !isAdminMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-3 border border-dashed rounded-md bg-muted/50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center mb-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-5 w-5 text-muted-foreground mb-2 animate-spin\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1239,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-center mb-1\",\n                                                                                            children: \"Out of credits\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1240,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        timeRemaining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-muted-foreground flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1243,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                \"Credits at \",\n                                                                                                timeRemaining\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1242,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1238,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: [\n                                                                                                \"Credits refill every \",\n                                                                                                (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditRegenerationPeriod)()\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1249,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            onClick: toggleAdminInput,\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            className: \"whitespace-nowrap\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                    className: \"mr-2 h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1258,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                \" Upgrade\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1252,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1248,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1237,\n                                                                            columnNumber: 29\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: handleExtract,\n                                                                            disabled: isExtracting || !selectedFile,\n                                                                            className: \"w-full\",\n                                                                            size: \"sm\",\n                                                                            children: isExtracting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loader__WEBPACK_IMPORTED_MODULE_8__.Loader, {\n                                                                                        size: \"sm\",\n                                                                                        className: \"mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1271,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \" Extracting...\"\n                                                                                ]\n                                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1275,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \" Extract Colors\"\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1263,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        extractionError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-red-500 mt-2\",\n                                                                            children: extractionError\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1281,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1232,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                                                    value: \"manual\",\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-3\",\n                                                                        children: creditState.credits === 0 && !isAdminMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-3 border border-dashed rounded-md bg-muted/50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-center mb-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-5 w-5 text-muted-foreground mb-2 animate-spin\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1290,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-center mb-1\",\n                                                                                            children: \"Out of credits\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1291,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        timeRemaining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-muted-foreground flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1294,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                \"Credits at \",\n                                                                                                timeRemaining\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1293,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1289,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-muted-foreground\",\n                                                                                            children: [\n                                                                                                \"Credits refill every \",\n                                                                                                (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditRegenerationPeriod)()\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1300,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            onClick: toggleAdminInput,\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            className: \"whitespace-nowrap\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                    className: \"mr-2 h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1309,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                \" Upgrade\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1303,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1299,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1288,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-3 h-3 rounded-full mr-2 bg-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1316,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs font-medium\",\n                                                                                            children: \"Ready to pick colors\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1317,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1315,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: \"Hover over the image to see a magnified view, then click to extract any color you want.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1322,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                currentPixelColor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"p-2 border rounded-md bg-card\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs mb-1 text-muted-foreground\",\n                                                                                            children: \"Current color:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1328,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-8 h-8 rounded-md border\",\n                                                                                                    style: {\n                                                                                                        backgroundColor: currentPixelColor\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1330,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-xs font-mono\",\n                                                                                                    children: currentPixelColor\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                    lineNumber: 1334,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1329,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1327,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"border-t pt-2 mt-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-[10px] text-muted-foreground\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium\",\n                                                                                                children: \"Tip:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                lineNumber: 1341,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            \" You can pick multiple colors without having to reselect this tab each time.\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1340,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1339,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1286,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1285,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1222,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        imageSize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Image size: \",\n                                                                    Math.round(imageSize.width),\n                                                                    \" \\xd7 \",\n                                                                    Math.round(imageSize.height)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1352,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1351,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"palette\",\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: extractedColors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Extracted Colors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1367,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    !isAdminMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: [\n                                                                            extractedColors.length,\n                                                                            \"/\",\n                                                                            COLOR_LIMIT_STANDARD\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1370,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: clearAllColors,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                            lineNumber: 1380,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1374,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1368,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1366,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 max-h-[500px] overflow-y-auto pr-1\",\n                                                        children: extractedColors.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-8 w-8 rounded-md mr-2 border\",\n                                                                        style: {\n                                                                            backgroundColor: color.hex\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1387,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm font-medium truncate\",\n                                                                                children: color.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1392,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: color.hex\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1393,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1391,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-8 w-8\",\n                                                                                onClick: ()=>copyColor(color.hex),\n                                                                                children: copiedColor === color.hex ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1402,\n                                                                                    columnNumber: 62\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1402,\n                                                                                    columnNumber: 94\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1396,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                                onClick: ()=>removeColor(index),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                    lineNumber: 1410,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1404,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1395,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1386,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1384,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            onClick: handleShowCodePreview,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1418,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" View Code\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1417,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1416,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center justify-center p-8 text-center text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-12 w-12 mb-4 opacity-20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1424,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-2\",\n                                                        children: \"No colors extracted yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1425,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Upload an image and use AI or the color picker to extract colors\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1426,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1423,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1363,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1362,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 1198,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 1197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 p-6 overflow-y-auto\",\n                            children: [\n                                selectedImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                            ref: canvasRef,\n                                            onClick: handleCanvasClick,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseEnter: handleMouseEnter,\n                                            onMouseLeave: handleMouseLeave,\n                                            className: \"border border-border rounded-lg max-w-full shadow-md \".concat(isPickingColor ? \"cursor-crosshair\" : \"\"),\n                                            style: {\n                                                maxHeight: \"70vh\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1438,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"destructive\",\n                                            size: \"icon\",\n                                            className: \"absolute top-2 right-2 h-8 w-8 shadow-md hover:bg-red-600 z-10\",\n                                            onClick: removeImage,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1455,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1449,\n                                            columnNumber: 19\n                                        }, this),\n                                        isPickingColor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 left-3 bg-background/90 text-foreground px-3 py-1.5 rounded-md text-sm shadow-xl border border-border\",\n                                            children: \"Click anywhere on the image to extract a color\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1459,\n                                            columnNumber: 19\n                                        }, this),\n                                        isPickingColor && showMagnifier && mousePosition && canvasRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pointer-events-none fixed\",\n                                            style: {\n                                                position: 'absolute',\n                                                left: getMagnifierPosition(mousePosition.x, mousePosition.y, canvasRef.current.getBoundingClientRect(), 150).left,\n                                                top: getMagnifierPosition(mousePosition.x, mousePosition.y, canvasRef.current.getBoundingClientRect(), 150).top,\n                                                zIndex: 50\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_magnifier__WEBPACK_IMPORTED_MODULE_11__.Magnifier, {\n                                                sourceCanvas: canvasRef.current,\n                                                x: mousePosition.x,\n                                                y: mousePosition.y,\n                                                zoomLevel: 5,\n                                                size: 150\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1473,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1464,\n                                            columnNumber: 19\n                                        }, this),\n                                        isExtracting && extractionMethod === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-background/70 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg border border-border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"h-12 w-12 text-primary animate-pulse mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1487,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-6 w-6 rounded-full border-2 border-primary border-t-transparent animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1489,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1488,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1486,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-2\",\n                                                    children: \"AI Analyzing Image\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1492,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-muted-foreground mb-4 max-w-md px-4\",\n                                                    children: \"Extracting the most prominent and visually important colors in your image...\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1493,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1485,\n                                            columnNumber: 19\n                                        }, this),\n                                        creditState.credits === 0 && !isAdminMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-background/80 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg border border-border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-12 w-12 text-muted-foreground mb-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1501,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium mb-2\",\n                                                    children: \"Credits Depleted\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1502,\n                                                    columnNumber: 21\n                                                }, this),\n                                                timeRemaining && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-muted-foreground mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1505,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Credits available at \",\n                                                                timeRemaining\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1506,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1504,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-muted-foreground mb-4 max-w-md px-4\",\n                                                    children: [\n                                                        \"Credits automatically refill every \",\n                                                        (0,_lib_credit_service__WEBPACK_IMPORTED_MODULE_14__.getCreditRegenerationPeriod)(),\n                                                        \", or you can upgrade now for unlimited access.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1509,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            onClick: showCreditStatus,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1514,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" Check Status\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1513,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: toggleAdminInput,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                    lineNumber: 1517,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" Upgrade Now\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1516,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1512,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1500,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1437,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center h-full text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-12 border-2 border-dashed border-border rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 text-muted-foreground mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1526,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"No Image Selected\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1527,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground mb-4 max-w-md\",\n                                                children: \"Upload an image to extract colors using AI or manually pick colors with the eyedropper tool.\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1528,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>{\n                                                    handleUploadClick();\n                                                    setActiveTab(\"upload\");\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                        lineNumber: 1535,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Upload Image\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1531,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1525,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1524,\n                                    columnNumber: 15\n                                }, this),\n                                extractedColors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Extracted Color Palette\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1544,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                            children: extractedColors.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"overflow-hidden border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-28\",\n                                                            style: {\n                                                                backgroundColor: color.hex\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1548,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: color.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1555,\n                                                                                columnNumber: 21\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: color.hex\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1556,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1554,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"icon\",\n                                                                                            onClick: ()=>copyColor(color.hex),\n                                                                                            children: copiedColor === color.hex ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                lineNumber: 1562,\n                                                                                                columnNumber: 64\n                                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                lineNumber: 1562,\n                                                                                                columnNumber: 96\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1561,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1560,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                                                        side: \"left\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Copy color\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1566,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1565,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1559,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"icon\",\n                                                                                            onClick: ()=>removeColor(index),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Clock_Code_Copy_Cpu_CreditCard_Download_Hand_LayoutPanelTop_LogIn_LogOut_Pipette_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                                lineNumber: 1572,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1571,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1570,\n                                                                                        columnNumber: 23\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                                                        side: \"left\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Remove color\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                            lineNumber: 1576,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                        lineNumber: 1575,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                                lineNumber: 1569,\n                                                                                columnNumber: 21\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                        lineNumber: 1558,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                                lineNumber: 1553,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                            lineNumber: 1552,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                    lineNumber: 1547,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                            lineNumber: 1545,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                    lineNumber: 1543,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                            lineNumber: 1435,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 1195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"border-t p-2 text-center text-xs text-muted-foreground\",\n                    children: \"Coloriqo - The right color tool\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 1591,\n                    columnNumber: 9\n                }, this),\n                showCodePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_code_preview__WEBPACK_IMPORTED_MODULE_10__.CodePreview, {\n                    colors: allColors,\n                    onClose: ()=>setShowCodePreview(false)\n                }, void 0, false, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 1597,\n                    columnNumber: 11\n                }, this),\n                showAdminInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center\",\n                    onClick: (e)=>{\n                        // Close dialog when clicking on backdrop (outside the modal)\n                        if (e.target === e.currentTarget) {\n                            setShowAdminInput(false);\n                        }\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-card border rounded-lg shadow-lg w-96 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"Enter Admin Code\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 1615,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: adminCodeInput,\n                                        onChange: (e)=>setAdminCodeInput(e.target.value),\n                                        placeholder: \"Enter admin code...\",\n                                        className: \"w-full p-2 border rounded-md bg-background text-foreground\",\n                                        onKeyDown: (e)=>{\n                                            if (e.key === 'Enter') {\n                                                verifyAdminCode(adminCodeInput);\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1617,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setShowAdminInput(false),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1630,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>verifyAdminCode(adminCodeInput),\n                                                children: \"Submit\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                                lineNumber: 1636,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                        lineNumber: 1629,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                                lineNumber: 1616,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                        lineNumber: 1614,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n                    lineNumber: 1605,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n            lineNumber: 909,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\components\\\\color-cards.tsx\",\n        lineNumber: 908,\n        columnNumber: 5\n    }, this);\n}\n_s(ColorCards, \"2Qt3I7IC0+MTP79ceKXJGM5WqOg=\", false, function() {\n    return [\n        _hooks_use_color_extractor__WEBPACK_IMPORTED_MODULE_7__.useColorExtractor\n    ];\n});\n_c = ColorCards;\nvar _c;\n$RefreshReg$(_c, \"ColorCards\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/color-cards.tsx\n"));

/***/ })

});