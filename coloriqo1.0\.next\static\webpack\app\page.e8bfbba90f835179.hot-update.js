"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_feature_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/feature-card */ \"(app-pages-browser)/./components/ui/feature-card.tsx\");\n/* harmony import */ var _components_ui_gradient_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/gradient-button */ \"(app-pages-browser)/./components/ui/gradient-button.tsx\");\n/* harmony import */ var _components_ui_theme_switcher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/theme-switcher */ \"(app-pages-browser)/./components/ui/theme-switcher.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer-click.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-panel-top.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Define features for the app\nconst FEATURES = [\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"AI-Powered Extraction\",\n        description: \"Extract colors intelligently using advanced AI algorithms for optimal palette generation.\",\n        iconColor: \"bg-blue-100 text-blue-500 dark:bg-blue-900/50 dark:text-blue-400\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Custom Color Naming\",\n        description: \"Get semantic names for your colors that help communicate and organize your palette.\",\n        iconColor: \"bg-purple-100 text-purple-500 dark:bg-purple-900/50 dark:text-purple-400\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Precision Picking\",\n        description: \"Pixel-perfect color selection with magnified precision tools.\",\n        iconColor: \"bg-pink-100 text-pink-500 dark:bg-pink-900/50 dark:text-pink-400\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: \"Export to Code\",\n        description: \"Export your palette directly for immediate use.\",\n        iconColor: \"bg-orange-100 text-orange-500 dark:bg-orange-900/50 dark:text-orange-400\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        title: \"Copy Formats\",\n        description: \"Copy colors in HEX formats with a single click.\",\n        iconColor: \"bg-emerald-100 text-emerald-500 dark:bg-emerald-900/50 dark:text-emerald-400\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        title: \"One-Click Export\",\n        description: \"Save your palettes in various formats for use in all major design tools.\",\n        iconColor: \"bg-indigo-100 text-indigo-500 dark:bg-indigo-900/50 dark:text-indigo-400\"\n    }\n];\n// Testimonials data\nconst TESTIMONIALS = [\n    {\n        quote: \"Coloriqo changed my workflow completely. I save hours on every project by extracting the perfect palette instantly.\",\n        name: \"Sarah Johnson\",\n        title: \"Senior UI Designer\",\n        avatar: \"https://i.pravatar.cc/150?img=32\"\n    },\n    {\n        quote: \"The AI color naming feature is brilliant. No more struggling to name shades in my design system documentation.\",\n        name: \"Michael Torres\",\n        title: \"Product Designer\",\n        avatar: \"https://i.pravatar.cc/150?img=59\"\n    },\n    {\n        quote: \"As a developer, the code export options are fantastic. Perfect integration with my CSS variables.\",\n        name: \"Leila Khan\",\n        title: \"Frontend Developer\",\n        avatar: \"https://i.pravatar.cc/150?img=48\"\n    }\n];\n// Simple Floating Scroll To Top Button\nfunction FloatingScrollButton() {\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FloatingScrollButton.useEffect\": ()=>{\n            const toggleVisibility = {\n                \"FloatingScrollButton.useEffect.toggleVisibility\": ()=>{\n                    setIsVisible(window.scrollY > 300);\n                }\n            }[\"FloatingScrollButton.useEffect.toggleVisibility\"];\n            window.addEventListener(\"scroll\", toggleVisibility);\n            return ({\n                \"FloatingScrollButton.useEffect\": ()=>window.removeEventListener(\"scroll\", toggleVisibility)\n            })[\"FloatingScrollButton.useEffect\"];\n        }\n    }[\"FloatingScrollButton.useEffect\"], []);\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n        onClick: scrollToTop,\n        className: \"fixed right-8 top-1/2 z-50 rounded-full p-3 shadow-lg transition-all duration-500\\n        bg-gradient-to-r from-purple-500 to-pink-500 hover:from-pink-500 hover:to-purple-500\\n        border-2 border-white/20 backdrop-blur-sm scroll-to-top-btn\\n        \".concat(isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-0 pointer-events-none'),\n        size: \"icon\",\n        \"aria-label\": \"Scroll to top\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-5 w-5 text-white\"\n        }, void 0, false, {\n            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(FloatingScrollButton, \"J3yJOyGdBT4L7hs1p1XQYVGMdrY=\");\n_c = FloatingScrollButton;\nfunction Home() {\n    _s1();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTestimonial, setActiveTestimonial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isHoveringDemo, setIsHoveringDemo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // State for mobile menu toggle\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setMounted(true);\n            // Auto-rotate testimonials\n            const interval = setInterval({\n                \"Home.useEffect.interval\": ()=>{\n                    setActiveTestimonial({\n                        \"Home.useEffect.interval\": (prev)=>(prev + 1) % TESTIMONIALS.length\n                    }[\"Home.useEffect.interval\"]);\n                }\n            }[\"Home.useEffect.interval\"], 5000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-background font-sans antialiased relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pointer-events-none fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.08),transparent_70%)]\"\n            }, void 0, false, {\n                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-1.5 w-full bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500\"\n            }, void 0, false, {\n                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"container mx-auto px-4 py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 ml-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 text-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md\",\n                                                        style: {\n                                                            maskImage: 'url(\"data:image/svg+xml,%3Csvg xmlns=\\'http://www.w3.org/2000/svg\\' width=\\'24\\' height=\\'24\\' viewBox=\\'0 0 24 24\\' fill=\\'none\\' stroke=\\'currentColor\\' stroke-width=\\'2\\' stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\'%3E%3Cpath d=\\'M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121\\'/%3E%3C/svg%3E\")',\n                                                            maskSize: 'cover'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400\",\n                                                style: {\n                                                    fontFamily: \"var(--font-montserrat)\",\n                                                    letterSpacing: \"-0.5px\",\n                                                    fontWeight: \"800\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                    href: \"/tool\",\n                                                    \"data-barba\": \"wrapper\",\n                                                    children: \"Coloriqo\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 16\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"flex gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#features\",\n                                                        className: \"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\",\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#testimonials\",\n                                                        className: \"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\",\n                                                        children: \"Testimonials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#pricing\",\n                                                        className: \"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\",\n                                                        children: \"Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_switcher__WEBPACK_IMPORTED_MODULE_5__.ThemeSwitcher, {}, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                    href: \"/tool\",\n                                                    \"data-barba\": \"wrapper\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex md:hidden items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_switcher__WEBPACK_IMPORTED_MODULE_5__.ThemeSwitcher, {}, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                className: \"text-xs px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                    href: \"/tool\",\n                                                    \"data-barba\": \"wrapper\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                className: \"h-9 w-9 relative overflow-hidden group hover:bg-accent transition-colors\",\n                                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                                \"aria-label\": \"Toggle navigation menu\",\n                                                \"aria-expanded\": isMobileMenuOpen,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col justify-center items-center w-5 h-5 relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform \".concat(isMobileMenuOpen ? 'rotate-45 translate-y-0' : '-translate-y-1.5')\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out \".concat(isMobileMenuOpen ? 'opacity-0 scale-0' : 'opacity-100 scale-100')\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform \".concat(isMobileMenuOpen ? '-rotate-45 translate-y-0' : 'translate-y-1.5')\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden fixed inset-0 z-50 transition-all duration-300 \".concat(isMobileMenuOpen ? 'visible' : 'invisible'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-black/50 transition-opacity duration-300 \".concat(isMobileMenuOpen ? 'opacity-100' : 'opacity-0'),\n                                        onClick: ()=>setIsMobileMenuOpen(false)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 top-0 h-full w-64 bg-background border-l shadow-lg transform transition-transform duration-300 \".concat(isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Menu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        className: \"h-8 w-8 hover:bg-accent transition-colors\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        \"aria-label\": \"Close menu\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-4 h-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"absolute block h-0.5 w-4 bg-current rounded-full rotate-45 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"absolute block h-0.5 w-4 bg-current rounded-full -rotate-45 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"flex flex-col p-4 space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#features\",\n                                                        className: \"text-base font-medium text-muted-foreground hover:text-foreground transition-colors py-2 border-b border-border/50\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#testimonials\",\n                                                        className: \"text-base font-medium text-muted-foreground hover:text-foreground transition-colors py-2 border-b border-border/50\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        children: \"Testimonials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#pricing\",\n                                                        className: \"text-base font-medium text-muted-foreground hover:text-foreground transition-colors py-2 border-b border-border/50\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        children: \"Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 md:py-28\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center text-center max-w-5xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 inline-flex items-center rounded-full border border-neutral-200 dark:border-neutral-800 px-3 py-1 text-sm gap-1 bg-background shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex h-2 w-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-muted-foreground\",\n                                                children: \"Now with AI-powered extraction\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-6xl font-heading font-bold tracking-tight mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 animate-gradient-x bg-size-200\",\n                                                children: \"Transform Any Image\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Into the Perfect Color Palette\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-muted-foreground max-w-3xl mb-10\",\n                                        children: \"Extract harmonious colors from any image with AI precision. Build perfect palettes for your design projects in seconds, not hours.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 mb-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gradient_button__WEBPACK_IMPORTED_MODULE_4__.GradientButton, {\n                                                size: \"lg\",\n                                                className: \"px-8 py-6 font-medium text-base\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                    href: \"/tool\",\n                                                    className: \"flex items-center gap-2 justify-center\",\n                                                    \"data-barba\": \"wrapper\",\n                                                    children: [\n                                                        \"Start extracting colors \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                className: \"px-8 py-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center gap-2 justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                        href: \"#video\",\n                                                        children: \"Watch demo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full max-w-6xl perspective-1000\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-4 -inset-x-4 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 rounded-xl blur-xl -z-10 transition-all duration-500\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-xl overflow-hidden border border-neutral-200 dark:border-neutral-800 bg-card shadow-2xl transform transition-all duration-500\",\n                                                style: {\n                                                    transform: isHoveringDemo ? 'rotateX(2deg) translateY(-4px)' : 'rotateX(0) translateY(0)',\n                                                    boxShadow: isHoveringDemo ? '0 25px 50px -12px rgba(0, 0, 0, 0.08)' : '0 10px 30px -15px rgba(0, 0, 0, 0.08)'\n                                                },\n                                                onMouseEnter: ()=>setIsHoveringDemo(true),\n                                                onMouseLeave: ()=>setIsHoveringDemo(false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-2 bg-muted/70 border-b border-neutral-200 dark:border-neutral-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full bg-red-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full bg-yellow-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full bg-green-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground font-medium\",\n                                                                children: \"Coloriqo - Color Extraction Tool\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        id: \"video\",\n                                                        className: \"scroll-mt-12\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                            src: \"https://res.cloudinary.com/didt1ywys/video/upload/v1749389530/cursorful-video-1749385801624_jwfjsx.mp4\",\n                                                            autoPlay: true,\n                                                            muted: true,\n                                                            playsInline: true,\n                                                            loop: true\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"features\",\n                        className: \"py-24 bg-muted/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl md:text-4xl font-heading font-bold mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-purple-500\",\n                                                children: \"Powerful Features\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-muted-foreground max-w-2xl mx-auto\",\n                                            children: \"Everything you need to extract, refine, and utilize color palettes\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto\",\n                                    children: FEATURES.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_feature_card__WEBPACK_IMPORTED_MODULE_3__.FeatureCard, {\n                                            icon: feature.icon,\n                                            title: feature.title,\n                                            description: feature.description,\n                                            iconColor: feature.iconColor,\n                                            className: \"transition-all duration-300 hover:shadow-md hover:-translate-y-1 border border-neutral-200 dark:border-neutral-800\"\n                                        }, index, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"testimonials\",\n                        className: \"py-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl md:text-4xl font-heading font-bold mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-clip-text text-transparent bg-gradient-to-r from-pink-500 to-orange-500\",\n                                                children: \"Loved by Designers & Developers\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-muted-foreground max-w-2xl mx-auto\",\n                                            children: \"See why professionals choose Coloriqo for their color extraction needs\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-hidden relative h-72\",\n                                            children: TESTIMONIALS.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 transition-all duration-500 flex flex-col items-center justify-center p-8 rounded-xl border border-neutral-200 dark:border-neutral-800 bg-card\",\n                                                    style: {\n                                                        opacity: index === activeTestimonial ? 1 : 0,\n                                                        transform: \"translateX(\".concat((index - activeTestimonial) * 100, \"%)\"),\n                                                        zIndex: index === activeTestimonial ? 10 : 0\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4,\n                                                                5\n                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"inline-block h-4 w-4 text-amber-400 fill-amber-400\"\n                                                                }, star, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-medium text-center mb-6\",\n                                                            children: [\n                                                                '\"',\n                                                                testimonial.quote,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: testimonial.avatar,\n                                                                    alt: testimonial.name,\n                                                                    className: \"w-10 h-10 rounded-full mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: testimonial.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 420,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: testimonial.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center gap-2 mt-8\",\n                                            children: TESTIMONIALS.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTestimonial(index),\n                                                    className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(index === activeTestimonial ? 'bg-primary w-4' : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'),\n                                                    \"aria-label\": \"View testimonial \".concat(index + 1)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-muted/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto rounded-2xl bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 p-8 md:p-12 border border-neutral-200 dark:border-neutral-800 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl md:text-4xl font-heading font-bold mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500\",\n                                            children: \"Ready to Transform Your Design Workflow?\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-muted-foreground max-w-2xl mx-auto mb-8\",\n                                        children: \"Join thousands of designers extracting perfect color palettes in seconds.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gradient_button__WEBPACK_IMPORTED_MODULE_4__.GradientButton, {\n                                        size: \"lg\",\n                                        className: \"px-8 py-6 font-medium text-base\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/tool\",\n                                            className: \"flex items-center gap-2 justify-center\",\n                                            \"data-barba\": \"wrapper\",\n                                            children: [\n                                                \"Start for free \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 34\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-4\",\n                                        children: \"No credit card required. 10 free extractions included.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"py-12 border-t border-neutral-200 dark:border-neutral-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"hidden md:flex items-center gap-2 ml-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md\",\n                                                                    style: {\n                                                                        maskImage: 'url(\"data:image/svg+xml,%3Csvg xmlns=\\'http://www.w3.org/2000/svg\\' width=\\'24\\' height=\\'24\\' viewBox=\\'0 0 24 24\\' fill=\\'none\\' stroke=\\'currentColor\\' stroke-width=\\'2\\' stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\'%3E%3Cpath d=\\'M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121\\'/%3E%3C/svg%3E\")',\n                                                                        maskSize: 'cover'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400\",\n                                                            style: {\n                                                                fontFamily: \"var(--font-montserrat)\",\n                                                                letterSpacing: \"-0.5px\",\n                                                                fontWeight: \"800\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                                href: \"/tool\",\n                                                                \"data-barba\": \"wrapper\",\n                                                                children: \"Coloriqo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 16\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mt-2\",\n                                                    children: \"AI-powered color extraction for perfect palettes.\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Product\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Pricing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Changelog\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Resources\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Documentation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Tutorials\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Blog\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Company\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"About us\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Contact\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Privacy Policy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-12 pt-6 border-t border-neutral-200 dark:border-neutral-800 flex flex-col md:flex-row justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-4 md:mb-0\",\n                                            children: [\n                                                \"\\xa9 \",\n                                                new Date().getFullYear(),\n                                                \" Coloriqo. All rights reserved.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M5.026 15c6.038 0 9.341-5.003 9.341-9.334q0-.265-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingScrollButton, {}, void 0, false, {\n                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                lineNumber: 546,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s1(Home, \"otlBjEod8k5mHHAw8V38JMZX4/8=\");\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"FloatingScrollButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});