"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_feature_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/feature-card */ \"(app-pages-browser)/./components/ui/feature-card.tsx\");\n/* harmony import */ var _components_ui_gradient_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/gradient-button */ \"(app-pages-browser)/./components/ui/gradient-button.tsx\");\n/* harmony import */ var _components_ui_theme_switcher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/theme-switcher */ \"(app-pages-browser)/./components/ui/theme-switcher.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer-click.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-panel-top.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ArrowUp,ChevronRight,Code,Copy,Cpu,Download,LayoutPanelTop,MousePointerClick,Palette,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Define features for the app\nconst FEATURES = [\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"AI-Powered Extraction\",\n        description: \"Extract colors intelligently using advanced AI algorithms for optimal palette generation.\",\n        iconColor: \"bg-blue-100 text-blue-500 dark:bg-blue-900/50 dark:text-blue-400\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Custom Color Naming\",\n        description: \"Get semantic names for your colors that help communicate and organize your palette.\",\n        iconColor: \"bg-purple-100 text-purple-500 dark:bg-purple-900/50 dark:text-purple-400\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Precision Picking\",\n        description: \"Pixel-perfect color selection with magnified precision tools.\",\n        iconColor: \"bg-pink-100 text-pink-500 dark:bg-pink-900/50 dark:text-pink-400\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: \"Export to Code\",\n        description: \"Export your palette directly for immediate use.\",\n        iconColor: \"bg-orange-100 text-orange-500 dark:bg-orange-900/50 dark:text-orange-400\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        title: \"Copy Formats\",\n        description: \"Copy colors in HEX formats with a single click.\",\n        iconColor: \"bg-emerald-100 text-emerald-500 dark:bg-emerald-900/50 dark:text-emerald-400\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        title: \"One-Click Export\",\n        description: \"Save your palettes in various formats for use in all major design tools.\",\n        iconColor: \"bg-indigo-100 text-indigo-500 dark:bg-indigo-900/50 dark:text-indigo-400\"\n    }\n];\n// Testimonials data\nconst TESTIMONIALS = [\n    {\n        quote: \"Coloriqo changed my workflow completely. I save hours on every project by extracting the perfect palette instantly.\",\n        name: \"Sarah Johnson\",\n        title: \"Senior UI Designer\",\n        avatar: \"https://i.pravatar.cc/150?img=32\"\n    },\n    {\n        quote: \"The AI color naming feature is brilliant. No more struggling to name shades in my design system documentation.\",\n        name: \"Michael Torres\",\n        title: \"Product Designer\",\n        avatar: \"https://i.pravatar.cc/150?img=59\"\n    },\n    {\n        quote: \"As a developer, the code export options are fantastic. Perfect integration with my CSS variables.\",\n        name: \"Leila Khan\",\n        title: \"Frontend Developer\",\n        avatar: \"https://i.pravatar.cc/150?img=48\"\n    }\n];\n// Simple Floating Scroll To Top Button\nfunction FloatingScrollButton() {\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FloatingScrollButton.useEffect\": ()=>{\n            const toggleVisibility = {\n                \"FloatingScrollButton.useEffect.toggleVisibility\": ()=>{\n                    setIsVisible(window.scrollY > 300);\n                }\n            }[\"FloatingScrollButton.useEffect.toggleVisibility\"];\n            window.addEventListener(\"scroll\", toggleVisibility);\n            return ({\n                \"FloatingScrollButton.useEffect\": ()=>window.removeEventListener(\"scroll\", toggleVisibility)\n            })[\"FloatingScrollButton.useEffect\"];\n        }\n    }[\"FloatingScrollButton.useEffect\"], []);\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n        onClick: scrollToTop,\n        className: \"fixed right-8 top-1/2 z-50 rounded-full p-3 shadow-lg transition-all duration-500\\n        bg-gradient-to-r from-purple-500 to-pink-500 hover:from-pink-500 hover:to-purple-500\\n        border-2 border-white/20 backdrop-blur-sm scroll-to-top-btn\\n        \".concat(isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-0 pointer-events-none'),\n        size: \"icon\",\n        \"aria-label\": \"Scroll to top\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-5 w-5 text-white\"\n        }, void 0, false, {\n            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(FloatingScrollButton, \"J3yJOyGdBT4L7hs1p1XQYVGMdrY=\");\n_c = FloatingScrollButton;\nfunction Home() {\n    _s1();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTestimonial, setActiveTestimonial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isHoveringDemo, setIsHoveringDemo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // State for mobile menu toggle\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setMounted(true);\n            // Auto-rotate testimonials\n            const interval = setInterval({\n                \"Home.useEffect.interval\": ()=>{\n                    setActiveTestimonial({\n                        \"Home.useEffect.interval\": (prev)=>(prev + 1) % TESTIMONIALS.length\n                    }[\"Home.useEffect.interval\"]);\n                }\n            }[\"Home.useEffect.interval\"], 5000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-background font-sans antialiased relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pointer-events-none fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.08),transparent_70%)]\"\n            }, void 0, false, {\n                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-1.5 w-full bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500\"\n            }, void 0, false, {\n                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"container mx-auto px-4 py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 ml-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 text-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md\",\n                                                        style: {\n                                                            maskImage: 'url(\"data:image/svg+xml,%3Csvg xmlns=\\'http://www.w3.org/2000/svg\\' width=\\'24\\' height=\\'24\\' viewBox=\\'0 0 24 24\\' fill=\\'none\\' stroke=\\'currentColor\\' stroke-width=\\'2\\' stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\'%3E%3Cpath d=\\'M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121\\'/%3E%3C/svg%3E\")',\n                                                            maskSize: 'cover'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400\",\n                                                style: {\n                                                    fontFamily: \"var(--font-montserrat)\",\n                                                    letterSpacing: \"-0.5px\",\n                                                    fontWeight: \"800\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                    href: \"/tool\",\n                                                    \"data-barba\": \"wrapper\",\n                                                    children: \"Coloriqo\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 16\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"flex gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#features\",\n                                                        className: \"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\",\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#testimonials\",\n                                                        className: \"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\",\n                                                        children: \"Testimonials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#pricing\",\n                                                        className: \"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\",\n                                                        children: \"Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_switcher__WEBPACK_IMPORTED_MODULE_5__.ThemeSwitcher, {}, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                    href: \"/tool\",\n                                                    \"data-barba\": \"wrapper\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex md:hidden items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_switcher__WEBPACK_IMPORTED_MODULE_5__.ThemeSwitcher, {}, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                className: \"text-xs px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                    href: \"/tool\",\n                                                    \"data-barba\": \"wrapper\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                className: \"h-9 w-9 relative overflow-hidden group hover:bg-accent transition-colors\",\n                                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                                \"aria-label\": \"Toggle navigation menu\",\n                                                \"aria-expanded\": isMobileMenuOpen,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col justify-center items-center w-5 h-5 relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform \".concat(isMobileMenuOpen ? 'rotate-45 translate-y-0' : '-translate-y-1.5')\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out \".concat(isMobileMenuOpen ? 'opacity-0 scale-0' : 'opacity-100 scale-100')\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform \".concat(isMobileMenuOpen ? '-rotate-45 translate-y-0' : 'translate-y-1.5')\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden fixed inset-0 z-50 transition-all duration-300 \".concat(isMobileMenuOpen ? 'visible' : 'invisible'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-black/50 transition-opacity duration-300 \".concat(isMobileMenuOpen ? 'opacity-100' : 'opacity-0'),\n                                        onClick: ()=>setIsMobileMenuOpen(false)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 top-0 h-full w-64 bg-background border-l shadow-lg transform transition-transform duration-300 \".concat(isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Menu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        className: \"h-8 w-8\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        \"aria-label\": \"Close menu\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"block h-0.5 w-4 bg-current rotate-45\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"block h-0.5 w-4 bg-current -rotate-45 -translate-y-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"flex flex-col p-4 space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#features\",\n                                                        className: \"text-base font-medium text-muted-foreground hover:text-foreground transition-colors py-2 border-b border-border/50\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#testimonials\",\n                                                        className: \"text-base font-medium text-muted-foreground hover:text-foreground transition-colors py-2 border-b border-border/50\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        children: \"Testimonials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#pricing\",\n                                                        className: \"text-base font-medium text-muted-foreground hover:text-foreground transition-colors py-2 border-b border-border/50\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        children: \"Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 md:py-28\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center text-center max-w-5xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 inline-flex items-center rounded-full border border-neutral-200 dark:border-neutral-800 px-3 py-1 text-sm gap-1 bg-background shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex h-2 w-2 rounded-full bg-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-muted-foreground\",\n                                                children: \"Now with AI-powered extraction\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-6xl font-heading font-bold tracking-tight mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 animate-gradient-x bg-size-200\",\n                                                children: \"Transform Any Image\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Into the Perfect Color Palette\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-muted-foreground max-w-3xl mb-10\",\n                                        children: \"Extract harmonious colors from any image with AI precision. Build perfect palettes for your design projects in seconds, not hours.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 mb-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gradient_button__WEBPACK_IMPORTED_MODULE_4__.GradientButton, {\n                                                size: \"lg\",\n                                                className: \"px-8 py-6 font-medium text-base\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                    href: \"/tool\",\n                                                    className: \"flex items-center gap-2 justify-center\",\n                                                    \"data-barba\": \"wrapper\",\n                                                    children: [\n                                                        \"Start extracting colors \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                className: \"px-8 py-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center gap-2 justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                        href: \"#video\",\n                                                        children: \"Watch demo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full max-w-6xl perspective-1000\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-4 -inset-x-4 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 rounded-xl blur-xl -z-10 transition-all duration-500\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-xl overflow-hidden border border-neutral-200 dark:border-neutral-800 bg-card shadow-2xl transform transition-all duration-500\",\n                                                style: {\n                                                    transform: isHoveringDemo ? 'rotateX(2deg) translateY(-4px)' : 'rotateX(0) translateY(0)',\n                                                    boxShadow: isHoveringDemo ? '0 25px 50px -12px rgba(0, 0, 0, 0.08)' : '0 10px 30px -15px rgba(0, 0, 0, 0.08)'\n                                                },\n                                                onMouseEnter: ()=>setIsHoveringDemo(true),\n                                                onMouseLeave: ()=>setIsHoveringDemo(false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-2 bg-muted/70 border-b border-neutral-200 dark:border-neutral-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1.5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full bg-red-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full bg-yellow-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full bg-green-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground font-medium\",\n                                                                children: \"Coloriqo - Color Extraction Tool\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        id: \"video\",\n                                                        className: \"scroll-mt-12\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                            src: \"https://res.cloudinary.com/didt1ywys/video/upload/v1749389530/cursorful-video-1749385801624_jwfjsx.mp4\",\n                                                            autoPlay: true,\n                                                            muted: true,\n                                                            playsInline: true,\n                                                            loop: true\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"features\",\n                        className: \"py-24 bg-muted/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl md:text-4xl font-heading font-bold mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-purple-500\",\n                                                children: \"Powerful Features\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-muted-foreground max-w-2xl mx-auto\",\n                                            children: \"Everything you need to extract, refine, and utilize color palettes\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto\",\n                                    children: FEATURES.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_feature_card__WEBPACK_IMPORTED_MODULE_3__.FeatureCard, {\n                                            icon: feature.icon,\n                                            title: feature.title,\n                                            description: feature.description,\n                                            iconColor: feature.iconColor,\n                                            className: \"transition-all duration-300 hover:shadow-md hover:-translate-y-1 border border-neutral-200 dark:border-neutral-800\"\n                                        }, index, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"testimonials\",\n                        className: \"py-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl md:text-4xl font-heading font-bold mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-clip-text text-transparent bg-gradient-to-r from-pink-500 to-orange-500\",\n                                                children: \"Loved by Designers & Developers\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-muted-foreground max-w-2xl mx-auto\",\n                                            children: \"See why professionals choose Coloriqo for their color extraction needs\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-hidden relative h-72\",\n                                            children: TESTIMONIALS.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 transition-all duration-500 flex flex-col items-center justify-center p-8 rounded-xl border border-neutral-200 dark:border-neutral-800 bg-card\",\n                                                    style: {\n                                                        opacity: index === activeTestimonial ? 1 : 0,\n                                                        transform: \"translateX(\".concat((index - activeTestimonial) * 100, \"%)\"),\n                                                        zIndex: index === activeTestimonial ? 10 : 0\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4,\n                                                                5\n                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"inline-block h-4 w-4 text-amber-400 fill-amber-400\"\n                                                                }, star, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 411,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-medium text-center mb-6\",\n                                                            children: [\n                                                                '\"',\n                                                                testimonial.quote,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: testimonial.avatar,\n                                                                    alt: testimonial.name,\n                                                                    className: \"w-10 h-10 rounded-full mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: testimonial.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: testimonial.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 419,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center gap-2 mt-8\",\n                                            children: TESTIMONIALS.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveTestimonial(index),\n                                                    className: \"w-2 h-2 rounded-full transition-all duration-300 \".concat(index === activeTestimonial ? 'bg-primary w-4' : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'),\n                                                    \"aria-label\": \"View testimonial \".concat(index + 1)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-muted/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto rounded-2xl bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 p-8 md:p-12 border border-neutral-200 dark:border-neutral-800 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl md:text-4xl font-heading font-bold mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500\",\n                                            children: \"Ready to Transform Your Design Workflow?\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-muted-foreground max-w-2xl mx-auto mb-8\",\n                                        children: \"Join thousands of designers extracting perfect color palettes in seconds.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gradient_button__WEBPACK_IMPORTED_MODULE_4__.GradientButton, {\n                                        size: \"lg\",\n                                        className: \"px-8 py-6 font-medium text-base\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/tool\",\n                                            className: \"flex items-center gap-2 justify-center\",\n                                            \"data-barba\": \"wrapper\",\n                                            children: [\n                                                \"Start for free \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 34\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-4\",\n                                        children: \"No credit card required. 10 free extractions included.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"py-12 border-t border-neutral-200 dark:border-neutral-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"hidden md:flex items-center gap-2 ml-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ArrowUp_ChevronRight_Code_Copy_Cpu_Download_LayoutPanelTop_MousePointerClick_Palette_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md\",\n                                                                    style: {\n                                                                        maskImage: 'url(\"data:image/svg+xml,%3Csvg xmlns=\\'http://www.w3.org/2000/svg\\' width=\\'24\\' height=\\'24\\' viewBox=\\'0 0 24 24\\' fill=\\'none\\' stroke=\\'currentColor\\' stroke-width=\\'2\\' stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\'%3E%3Cpath d=\\'M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121\\'/%3E%3C/svg%3E\")',\n                                                                        maskSize: 'cover'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400\",\n                                                            style: {\n                                                                fontFamily: \"var(--font-montserrat)\",\n                                                                letterSpacing: \"-0.5px\",\n                                                                fontWeight: \"800\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                                href: \"/tool\",\n                                                                \"data-barba\": \"wrapper\",\n                                                                children: \"Coloriqo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 16\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mt-2\",\n                                                    children: \"AI-powered color extraction for perfect palettes.\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Product\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Pricing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Changelog\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Resources\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Documentation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Tutorials\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Blog\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-3\",\n                                                    children: \"Company\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"About us\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Contact\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-foreground transition-colors\",\n                                                                children: \"Privacy Policy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-12 pt-6 border-t border-neutral-200 dark:border-neutral-800 flex flex-col md:flex-row justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-4 md:mb-0\",\n                                            children: [\n                                                \"\\xa9 \",\n                                                new Date().getFullYear(),\n                                                \" Coloriqo. All rights reserved.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M5.026 15c6.038 0 9.341-5.003 9.341-9.334q0-.265-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingScrollButton, {}, void 0, false, {\n                fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\WeWiseLabs\\\\coloriqo1.0\\\\app\\\\page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s1(Home, \"otlBjEod8k5mHHAw8V38JMZX4/8=\");\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"FloatingScrollButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});