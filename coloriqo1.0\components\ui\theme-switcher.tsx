"use client"

import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useEffect, useState } from "react"

export function ThemeSwitcher() {
  // Always call hooks at the top level - never conditionally
  const { theme, setTheme } = useTheme()
  const [isMounted, setIsMounted] = useState(false)
  
  // Use useEffect for client-side mounting detection
  useEffect(() => {
    setIsMounted(true)
  }, [])
  
  // For server-side rendering and initial client render before hydration
  // Return a placeholder with same dimensions but no dropdown functionality
  if (!isMounted) {
    return (
      <Button
        variant="outline"
        size="icon"
        className="h-8 w-8"
        disabled={false}
      >
        <Sun className="h-4 w-4" />
        <span className="sr-only">Theme</span>
      </Button>
    )
  }
  
  // For client-side rendering after hydration
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="h-8 w-8">
          <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          <Sun className="mr-2 h-4 w-4" />
          <span>Light</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          <Moon className="mr-2 h-4 w-4" />
          <span>Dark</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          <Monitor className="mr-2 h-4 w-4" />
          <span>System</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 