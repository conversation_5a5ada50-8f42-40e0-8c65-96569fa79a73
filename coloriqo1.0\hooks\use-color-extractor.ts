import { useState, useEffect } from "react";
import { Color, ColorResponse, ExtractionMethod } from "@/types";
import { toast } from "@/hooks/use-toast";


interface UseColorExtractorProps {
  onExtractedColors: (colors: Color[]) => void;
  canvasRef: React.RefObject<HTMLCanvasElement | null>;
  existingColors?: Color[];
  isAdminMode?: boolean;
  colorLimit?: number;
  initialImage?: string | null;
}

export function useColorExtractor({ 
  onExtractedColors, 
  canvasRef,
  existingColors = [],
  isAdminMode = false,
  colorLimit = 10,
  initialImage = null
}: UseColorExtractorProps) {
  const [isExtracting, setIsExtracting] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(initialImage);
  const [isPickingColor, setIsPickingColor] = useState(false);
  const [extractionError, setExtractionError] = useState<string | null>(null);
  const [imageSize, setImageSize] = useState<{ width: number; height: number } | null>(null);
  
  // Effect to draw image on canvas when selected
  useEffect(() => {
    if (selectedImage && canvasRef.current) {
      const img = new Image();
      img.crossOrigin = "anonymous";
      
      img.onload = () => {
        if (!canvasRef.current) return;
        
        const canvas = canvasRef.current;
        const ctx = canvas.getContext("2d");
        
        if (!ctx) return;
        
        // Calculate aspect ratio to fit within container
        // We'll use a larger canvas size for better quality color extraction
        const maxWidth = window.innerWidth * 0.7; // 70% of viewport width
        const maxHeight = window.innerHeight * 0.6; // 60% of viewport height
        
        let width = img.width;
        let height = img.height;
        
        // Scale down if needed while maintaining aspect ratio
        if (width > maxWidth) {
          const ratio = maxWidth / width;
          width = maxWidth;
          height = height * ratio;
        }
        
        if (height > maxHeight) {
          const ratio = maxHeight / height;
          height = height * ratio;
          width = width * ratio;
        }
        
        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;
        
        // Clear the canvas
        ctx.clearRect(0, 0, width, height);
        
        // Draw image on canvas
        ctx.drawImage(img, 0, 0, width, height);
        
        // Store image size for reference
        setImageSize({ width, height });
      };
      
      img.onerror = () => {
        toast({
          title: "Error loading image",
          description: "Could not load the selected image",
          variant: "destructive",
        });
      };
      
      img.src = selectedImage;
    }
  }, [selectedImage, canvasRef]);
  
  // Extract colors using AI (Gemini API)
  const extractColorsWithAI = async (imageFile: File) => {
    setIsExtracting(true);
    setExtractionError(null);
    
    try {
      const formData = new FormData();
      formData.append("image", imageFile);
      
      // Send existing colors in the request to help AI find new unique ones
      if (existingColors.length > 0) {
        formData.append("existingColors", JSON.stringify(
          existingColors.map(color => color.hex)
        ));
      }
      
      // Calculate how many colors to request based on admin status and existing colors
      const remainingSlots = isAdminMode ? 10 : (colorLimit - existingColors.length);
      // Request exactly 5 colors per extraction (1 credit = 5 colors)
      const maxColors = Math.min(5, remainingSlots);
      
      // Add parameter to request deeper color analysis
      formData.append("deepExtract", "true");
      
      // Add maxColors parameter to the request
      formData.append("maxColors", maxColors.toString());
      
      const response = await fetch("/api/extract-colors", {
        method: "POST",
        body: formData,
      });
      
      const data: ColorResponse = await response.json();
      
      if (!response.ok || data.error) {
        throw new Error(data.error || "Failed to extract colors");
      }
      
      if (data.colors && data.colors.length > 0) {
        // Filter out colors that already exist in the palette
        const existingHexColors = new Set(existingColors.map(c => c.hex.toLowerCase()));
        const uniqueNewColors = data.colors.filter(
          color => !existingHexColors.has(color.hex.toLowerCase())
        );
        
        // Limit the number of colors based on remaining slots
        const colorsToAdd = uniqueNewColors.slice(0, remainingSlots);
        
        // Always add the colors we found to the palette, even if fewer than requested
        if (colorsToAdd.length > 0) {
          onExtractedColors(colorsToAdd);
          
          // Create a descriptive message with color names
          const colorNames = colorsToAdd.map(color => color.name).join(', ');
          toast({
            title: "Colors extracted!",
            description: `Found ${colorsToAdd.length} new unique colors: ${colorNames}`,
            duration: 4000,
          });
        } else {
          // Only show the "no colors found" message when truly none are found
          toast({
            title: "No new colors found",
            description: "AI couldn't find any unique colors. Try manual picking mode for more precise selection.",
            duration: 3000,
          });
        }
      } else {
        throw new Error("No colors were extracted");
      }
    } catch (error) {
      console.error("Error extracting colors:", error);
      setExtractionError(
        error instanceof Error ? error.message : "Failed to extract colors"
      );
      toast({
        title: "Extraction failed",
        description: "Could not extract colors from the image. Try manual selection.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsExtracting(false);
    }
  };
  
  // Toggle color picking mode for manual extraction
  const toggleColorPicker = () => {
    setIsPickingColor(!isPickingColor);
  };
  
  // Set the selected image
  const setImage = (imageDataUrl: string) => {
    setSelectedImage(imageDataUrl);
    setExtractionError(null);
  };
  
  // Handle file selection
  const handleFileSelect = (file: File) => {
    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setImage(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
      return file;
    }
    return null;
  };
  
  // Extract colors using the specified method
  const extractColors = async (file: File, method: ExtractionMethod) => {
    if (method === "ai") {
      await extractColorsWithAI(file);
    } else {
      // For manual extraction, just enable the color picker
      toggleColorPicker();
    }
  };
  
  return {
    isExtracting,
    selectedImage,
    isPickingColor,
    extractionError,
    imageSize,
    setImage,
    toggleColorPicker,
    handleFileSelect,
    extractColors,
    setSelectedImage,
  };
} 